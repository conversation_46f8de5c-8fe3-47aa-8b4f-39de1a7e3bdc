[{"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App/grap_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App/motor_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App/pid_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray/IIC.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray/Time.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray/hardware_iic.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID/pid.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/empty.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/uart_app.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug", "command": "clang++ -D__MSPM0G3505__ -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/App\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Gray\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/PID\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Driver\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy\" -I\"C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug/ti_msp_dl_config.c"}]