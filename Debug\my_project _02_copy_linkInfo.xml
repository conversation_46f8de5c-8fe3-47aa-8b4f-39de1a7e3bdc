<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o my_project _02_copy.out -mmy_project _02_copy.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/my_project _02_copy -iC:/Users/<USER>/workspace_ccstheia/my_project _02_copy/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=my_project _02_copy_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./uart_app.o ./App/grap_app.o ./App/motor_app.o ./App/pid_app.o ./Driver/motor.o ./Gray/IIC.o ./Gray/Time.o ./Gray/hardware_iic.o ./PID/pid.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688b4c65</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\my_project _02_copy.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1b39</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\</path>
         <kind>object</kind>
         <file>uart_app.o</file>
         <name>uart_app.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\App\</path>
         <kind>object</kind>
         <file>grap_app.o</file>
         <name>grap_app.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\App\</path>
         <kind>object</kind>
         <file>motor_app.o</file>
         <name>motor_app.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\App\</path>
         <kind>object</kind>
         <file>pid_app.o</file>
         <name>pid_app.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\Driver\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\Gray\</path>
         <kind>object</kind>
         <file>IIC.o</file>
         <name>IIC.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\Gray\</path>
         <kind>object</kind>
         <file>Time.o</file>
         <name>Time.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\Gray\</path>
         <kind>object</kind>
         <file>hardware_iic.o</file>
         <name>hardware_iic.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\.\PID\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project _02_copy\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-1a">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-1b">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-1c">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-1d">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-f2">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-f3">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-f4">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixunsdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text:__TI_printfi_minimal</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x284</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.Set_Speed</name>
         <load_address>0x344</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x344</run_address>
         <size>0x1e8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.adddf3_subdf3</name>
         <load_address>0x52c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x52c</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-1f3">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x6be</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6be</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x6c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c0</run_address>
         <size>0x114</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.__divdf3</name>
         <load_address>0x7d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d4</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x8e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8e0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x9e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9e4</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.__muldf3</name>
         <load_address>0xacc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xacc</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.SYSCFG_DL_PWM_MOTOR_init</name>
         <load_address>0xbb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbb0</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.Gray_Task</name>
         <load_address>0xc8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc8c</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text</name>
         <load_address>0xd64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd64</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text.PID_Task</name>
         <load_address>0xe3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe3c</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.hardware_IIC_ReadByte</name>
         <load_address>0xef0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xef0</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.__mulsf3</name>
         <load_address>0xfa0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfa0</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x102c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x102c</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.pid_formula_positional</name>
         <load_address>0x10b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10b0</run_address>
         <size>0x84</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.__divsf3</name>
         <load_address>0x1134</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1134</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x11b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11b8</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1234</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1234</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.SYSCFG_DL_I2C_Gray_init</name>
         <load_address>0x12b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12b0</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x1314</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1314</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x1376</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1376</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.text.DL_I2C_startControllerTransfer</name>
         <load_address>0x13d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13d4</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.pid_init</name>
         <load_address>0x1424</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1424</run_address>
         <size>0x50</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x1474</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1474</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.text.DL_UART_init</name>
         <load_address>0x14c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14c0</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.SYSCFG_DL_UART_OPENMV_init</name>
         <load_address>0x1508</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1508</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.pid_out_limit</name>
         <load_address>0x1550</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1550</run_address>
         <size>0x48</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.__fixunsdfsi</name>
         <load_address>0x1598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1598</run_address>
         <size>0x42</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x15dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15dc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text.pid_constrain</name>
         <load_address>0x161c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x161c</run_address>
         <size>0x3e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0x165c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x165c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.text.DL_I2C_flushControllerTXFIFO</name>
         <load_address>0x1698</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1698</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x16d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16d4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-123">
         <name>.text.SYSCFG_DL_TIMER_1_init</name>
         <load_address>0x1710</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1710</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.__floatsisf</name>
         <load_address>0x174c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x174c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.__gtsf2</name>
         <load_address>0x1788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1788</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-71">
         <name>.text.main</name>
         <load_address>0x17c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1800</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1800</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.__eqsf2</name>
         <load_address>0x183c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x183c</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.__muldsi3</name>
         <load_address>0x1878</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1878</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.__fixsfsi</name>
         <load_address>0x18b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b4</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.sprintf</name>
         <load_address>0x18ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18ec</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-171">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1924</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1924</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.PID_Init</name>
         <load_address>0x1958</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1958</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x198c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x198c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x19c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19c0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x19ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19ec</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.__floatsidf</name>
         <load_address>0x1a18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a18</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1a44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a44</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ef">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1a6e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a6e</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-1ea">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1a96</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a96</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.DL_I2C_setControllerRXFIFOThreshold</name>
         <load_address>0x1ac0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ac0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-193">
         <name>.text.DL_I2C_setControllerTXFIFOThreshold</name>
         <load_address>0x1ae8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ae8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.__floatunsisf</name>
         <load_address>0x1b10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b10</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1b38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b38</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.text.DL_I2C_setAnalogGlitchFilterPulseWidth</name>
         <load_address>0x1b60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b60</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x1b86</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b86</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.Motor_On</name>
         <load_address>0x1bac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bac</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.text.memccpy</name>
         <load_address>0x1bd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bd0</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.pid_calculate_positional</name>
         <load_address>0x1bf2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bf2</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-170">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1c14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c14</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1c34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c34</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0x1c54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c54</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1c70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c70</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-196">
         <name>.text.DL_I2C_enableInterrupt</name>
         <load_address>0x1c8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c8c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x1ca8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ca8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1cc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cc4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x1ce0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ce0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x1cfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cfc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x1d18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d18</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1d34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d34</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x1d50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d50</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.IIC_ReadByte</name>
         <load_address>0x1d6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d6c</run_address>
         <size>0x1c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1d88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d88</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-164">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1da0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1da0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-172">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1db8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1db8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1dd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dd0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1de8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1de8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1e00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e00</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-190">
         <name>.text.DL_I2C_enableAnalogGlitchFilter</name>
         <load_address>0x1e18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e18</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-197">
         <name>.text.DL_I2C_enableController</name>
         <load_address>0x1e30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e30</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-195">
         <name>.text.DL_I2C_enableControllerClockStretching</name>
         <load_address>0x1e48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e48</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-166">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0x1e60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e60</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-162">
         <name>.text.DL_I2C_reset</name>
         <load_address>0x1e78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e78</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-192">
         <name>.text.DL_I2C_setTimerPeriod</name>
         <load_address>0x1e90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e90</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-175">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1ea8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ea8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text.DL_Timer_clearInterruptStatus</name>
         <load_address>0x1ec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ec0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-165">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1ed8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ed8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-161">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1ef0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ef0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1f08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f08</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x1f20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f20</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-167">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1f38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f38</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-163">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1f50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.IIC_Get_Digtal</name>
         <load_address>0x1f68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f68</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.TIMA1_IRQHandler</name>
         <load_address>0x1f80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text._outs</name>
         <load_address>0x1f98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f98</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1fb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fb0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x1fc6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fc6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1fdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fdc</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-173">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1ff0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ff0</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.text.DL_I2C_getControllerStatus</name>
         <load_address>0x2004</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2004</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.text.DL_I2C_receiveControllerData</name>
         <load_address>0x2018</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2018</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text.DL_I2C_resetControllerTransfer</name>
         <load_address>0x202c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x202c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x2040</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2040</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-180">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2054</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2054</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-181">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x2068</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2068</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x207c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x207c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x208e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x208e</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x20a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20a0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-177">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x20b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20b4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.DL_SYSCTL_enableSleepOnExit</name>
         <load_address>0x20c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20c4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x20d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20d4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.text.pid_set_target</name>
         <load_address>0x20e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20e4</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.text.__aeabi_memset</name>
         <load_address>0x20f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20f4</run_address>
         <size>0xe</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.text.strlen</name>
         <load_address>0x2102</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2102</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-119">
         <name>.text:TI_memset_small</name>
         <load_address>0x2110</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2110</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-168">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x211e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x211e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text._outc</name>
         <load_address>0x2128</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2128</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x2134</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2134</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text:abort</name>
         <load_address>0x213c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x213c</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x2142</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2142</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.HOSTexit</name>
         <load_address>0x2146</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2146</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x214a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x214a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.text._system_pre_init</name>
         <load_address>0x214e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x214e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-287">
         <name>.cinit..data.load</name>
         <load_address>0x21c0</load_address>
         <readonly>true</readonly>
         <run_address>0x21c0</run_address>
         <size>0x3a</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-285">
         <name>__TI_handler_table</name>
         <load_address>0x21fc</load_address>
         <readonly>true</readonly>
         <run_address>0x21fc</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-288">
         <name>.cinit..bss.load</name>
         <load_address>0x2208</load_address>
         <readonly>true</readonly>
         <run_address>0x2208</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-286">
         <name>__TI_cinit_table</name>
         <load_address>0x2210</load_address>
         <readonly>true</readonly>
         <run_address>0x2210</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-99">
         <name>.rodata.str1.6241306812405096412.1</name>
         <load_address>0x2158</load_address>
         <readonly>true</readonly>
         <run_address>0x2158</run_address>
         <size>0x22</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-198">
         <name>.rodata.gI2C_GrayClockConfig</name>
         <load_address>0x217a</load_address>
         <readonly>true</readonly>
         <run_address>0x217a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-188">
         <name>.rodata.gTIMER_1TimerConfig</name>
         <load_address>0x217c</load_address>
         <readonly>true</readonly>
         <run_address>0x217c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.rodata.str1.15363888844622738466.1</name>
         <load_address>0x2190</load_address>
         <readonly>true</readonly>
         <run_address>0x2190</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.rodata.gUART_OPENMVConfig</name>
         <load_address>0x21a2</load_address>
         <readonly>true</readonly>
         <run_address>0x21a2</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-183">
         <name>.rodata.gPWM_MOTORConfig</name>
         <load_address>0x21ac</load_address>
         <readonly>true</readonly>
         <run_address>0x21ac</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-182">
         <name>.rodata.gPWM_MOTORClockConfig</name>
         <load_address>0x21b4</load_address>
         <readonly>true</readonly>
         <run_address>0x21b4</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-187">
         <name>.rodata.gTIMER_1ClockConfig</name>
         <load_address>0x21b7</load_address>
         <readonly>true</readonly>
         <run_address>0x21b7</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.rodata.gUART_OPENMVClockConfig</name>
         <load_address>0x21ba</load_address>
         <readonly>true</readonly>
         <run_address>0x21ba</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-24d">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-98">
         <name>.data.Ch</name>
         <load_address>0x202001b8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001b8</run_address>
         <size>0x100</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.data.gray_weights</name>
         <load_address>0x202002b8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202002b8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.data.g_line_position_error</name>
         <load_address>0x202002f0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202002f0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.data.pid_params_line</name>
         <load_address>0x202002d8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202002d8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.data.pid_running</name>
         <load_address>0x202002f4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202002f4</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.data.basic_speed</name>
         <load_address>0x202002ec</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202002ec</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-126">
         <name>.common:gPWM_MOTORBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-127">
         <name>.common:gTIMER_1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-97">
         <name>.common:Digtal</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001b4</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-b2">
         <name>.common:pid_line</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200178</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-28a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x155</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-128">
         <name>.debug_abbrev</name>
         <load_address>0x155</load_address>
         <run_address>0x155</run_address>
         <size>0x1fc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x351</load_address>
         <run_address>0x351</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_abbrev</name>
         <load_address>0x3be</load_address>
         <run_address>0x3be</run_address>
         <size>0xc4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0x482</load_address>
         <run_address>0x482</run_address>
         <size>0x102</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_abbrev</name>
         <load_address>0x584</load_address>
         <run_address>0x584</run_address>
         <size>0x116</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.debug_abbrev</name>
         <load_address>0x69a</load_address>
         <run_address>0x69a</run_address>
         <size>0x181</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0x81b</load_address>
         <run_address>0x81b</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_abbrev</name>
         <load_address>0x8d8</load_address>
         <run_address>0x8d8</run_address>
         <size>0xb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1e7">
         <name>.debug_abbrev</name>
         <load_address>0x98a</load_address>
         <run_address>0x98a</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1eb">
         <name>.debug_abbrev</name>
         <load_address>0x9ec</load_address>
         <run_address>0x9ec</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_abbrev</name>
         <load_address>0xbd3</load_address>
         <run_address>0xbd3</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1ed">
         <name>.debug_abbrev</name>
         <load_address>0xe59</load_address>
         <run_address>0xe59</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_abbrev</name>
         <load_address>0x10f4</load_address>
         <run_address>0x10f4</run_address>
         <size>0xe1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x11d5</load_address>
         <run_address>0x11d5</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_abbrev</name>
         <load_address>0x1284</load_address>
         <run_address>0x1284</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_abbrev</name>
         <load_address>0x13f4</load_address>
         <run_address>0x13f4</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0x142d</load_address>
         <run_address>0x142d</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0x14ef</load_address>
         <run_address>0x14ef</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0x155f</load_address>
         <run_address>0x155f</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.debug_abbrev</name>
         <load_address>0x15ec</load_address>
         <run_address>0x15ec</run_address>
         <size>0x2a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0x188f</load_address>
         <run_address>0x188f</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1f0">
         <name>.debug_abbrev</name>
         <load_address>0x1927</load_address>
         <run_address>0x1927</run_address>
         <size>0x8b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.debug_abbrev</name>
         <load_address>0x19b2</load_address>
         <run_address>0x19b2</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_abbrev</name>
         <load_address>0x19de</load_address>
         <run_address>0x19de</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.debug_abbrev</name>
         <load_address>0x1a05</load_address>
         <run_address>0x1a05</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_abbrev</name>
         <load_address>0x1a2c</load_address>
         <run_address>0x1a2c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.debug_abbrev</name>
         <load_address>0x1a53</load_address>
         <run_address>0x1a53</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.debug_abbrev</name>
         <load_address>0x1a7a</load_address>
         <run_address>0x1a7a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_abbrev</name>
         <load_address>0x1aa1</load_address>
         <run_address>0x1aa1</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-155">
         <name>.debug_abbrev</name>
         <load_address>0x1ac8</load_address>
         <run_address>0x1ac8</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_abbrev</name>
         <load_address>0x1aef</load_address>
         <run_address>0x1aef</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_abbrev</name>
         <load_address>0x1b16</load_address>
         <run_address>0x1b16</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-154">
         <name>.debug_abbrev</name>
         <load_address>0x1b3d</load_address>
         <run_address>0x1b3d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_abbrev</name>
         <load_address>0x1b64</load_address>
         <run_address>0x1b64</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_abbrev</name>
         <load_address>0x1b8b</load_address>
         <run_address>0x1b8b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-153">
         <name>.debug_abbrev</name>
         <load_address>0x1bb2</load_address>
         <run_address>0x1bb2</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_abbrev</name>
         <load_address>0x1bd9</load_address>
         <run_address>0x1bd9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_abbrev</name>
         <load_address>0x1c00</load_address>
         <run_address>0x1c00</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-1f8">
         <name>.debug_abbrev</name>
         <load_address>0x1c25</load_address>
         <run_address>0x1c25</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_abbrev</name>
         <load_address>0x1c4c</load_address>
         <run_address>0x1c4c</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-229">
         <name>.debug_abbrev</name>
         <load_address>0x1c71</load_address>
         <run_address>0x1c71</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_abbrev</name>
         <load_address>0x1cca</load_address>
         <run_address>0x1cca</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_abbrev</name>
         <load_address>0x1cef</load_address>
         <run_address>0x1cef</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-28c">
         <name>.debug_abbrev</name>
         <load_address>0x1d14</load_address>
         <run_address>0x1d14</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa4f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_info</name>
         <load_address>0xa4f</load_address>
         <run_address>0xa4f</run_address>
         <size>0x36ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x40fc</load_address>
         <run_address>0x40fc</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0x417c</load_address>
         <run_address>0x417c</run_address>
         <size>0x144</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x42c0</load_address>
         <run_address>0x42c0</run_address>
         <size>0x8e1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_info</name>
         <load_address>0x4ba1</load_address>
         <run_address>0x4ba1</run_address>
         <size>0xeb7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-140">
         <name>.debug_info</name>
         <load_address>0x5a58</load_address>
         <run_address>0x5a58</run_address>
         <size>0x9a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_info</name>
         <load_address>0x6400</load_address>
         <run_address>0x6400</run_address>
         <size>0x36e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_info</name>
         <load_address>0x676e</load_address>
         <run_address>0x676e</run_address>
         <size>0x3ac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.debug_info</name>
         <load_address>0x6b1a</load_address>
         <run_address>0x6b1a</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_info</name>
         <load_address>0x6b8f</load_address>
         <run_address>0x6b8f</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_info</name>
         <load_address>0x7851</load_address>
         <run_address>0x7851</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.debug_info</name>
         <load_address>0xa9c3</load_address>
         <run_address>0xa9c3</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_info</name>
         <load_address>0xbc69</load_address>
         <run_address>0xbc69</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xbdce</load_address>
         <run_address>0xbdce</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_info</name>
         <load_address>0xc1f1</load_address>
         <run_address>0xc1f1</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_info</name>
         <load_address>0xc935</load_address>
         <run_address>0xc935</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_info</name>
         <load_address>0xc97b</load_address>
         <run_address>0xc97b</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0xcb0d</load_address>
         <run_address>0xcb0d</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xcbd3</load_address>
         <run_address>0xcbd3</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_info</name>
         <load_address>0xcd4f</load_address>
         <run_address>0xcd4f</run_address>
         <size>0x1f24</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_info</name>
         <load_address>0xec73</load_address>
         <run_address>0xec73</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.debug_info</name>
         <load_address>0xed6b</load_address>
         <run_address>0xed6b</run_address>
         <size>0xce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_info</name>
         <load_address>0xee39</load_address>
         <run_address>0xee39</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_info</name>
         <load_address>0xee74</load_address>
         <run_address>0xee74</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_info</name>
         <load_address>0xf01b</load_address>
         <run_address>0xf01b</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_info</name>
         <load_address>0xf1c2</load_address>
         <run_address>0xf1c2</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_info</name>
         <load_address>0xf34f</load_address>
         <run_address>0xf34f</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_info</name>
         <load_address>0xf4de</load_address>
         <run_address>0xf4de</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_info</name>
         <load_address>0xf66b</load_address>
         <run_address>0xf66b</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_info</name>
         <load_address>0xf7f8</load_address>
         <run_address>0xf7f8</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_info</name>
         <load_address>0xf985</load_address>
         <run_address>0xf985</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_info</name>
         <load_address>0xfb14</load_address>
         <run_address>0xfb14</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_info</name>
         <load_address>0xfca9</load_address>
         <run_address>0xfca9</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_info</name>
         <load_address>0xfe3c</load_address>
         <run_address>0xfe3c</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0xffcf</load_address>
         <run_address>0xffcf</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_info</name>
         <load_address>0x10166</load_address>
         <run_address>0x10166</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_info</name>
         <load_address>0x1037d</load_address>
         <run_address>0x1037d</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_info</name>
         <load_address>0x10516</load_address>
         <run_address>0x10516</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.debug_info</name>
         <load_address>0x106cb</load_address>
         <run_address>0x106cb</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_info</name>
         <load_address>0x10887</load_address>
         <run_address>0x10887</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-1f6">
         <name>.debug_info</name>
         <load_address>0x10a48</load_address>
         <run_address>0x10a48</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_info</name>
         <load_address>0x10acd</load_address>
         <run_address>0x10acd</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_info</name>
         <load_address>0x10dc7</load_address>
         <run_address>0x10dc7</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-28b">
         <name>.debug_info</name>
         <load_address>0x1100b</load_address>
         <run_address>0x1100b</run_address>
         <size>0xaf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_ranges</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x1d8</load_address>
         <run_address>0x1d8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_ranges</name>
         <load_address>0x1f0</load_address>
         <run_address>0x1f0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x208</load_address>
         <run_address>0x208</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_ranges</name>
         <load_address>0x230</load_address>
         <run_address>0x230</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_ranges</name>
         <load_address>0x260</load_address>
         <run_address>0x260</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_ranges</name>
         <load_address>0x2a8</load_address>
         <run_address>0x2a8</run_address>
         <size>0x60</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_ranges</name>
         <load_address>0x308</load_address>
         <run_address>0x308</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.debug_ranges</name>
         <load_address>0x370</load_address>
         <run_address>0x370</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_ranges</name>
         <load_address>0x548</load_address>
         <run_address>0x548</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_ranges</name>
         <load_address>0x720</load_address>
         <run_address>0x720</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_ranges</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x8e8</load_address>
         <run_address>0x8e8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_ranges</name>
         <load_address>0x930</load_address>
         <run_address>0x930</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_ranges</name>
         <load_address>0x978</load_address>
         <run_address>0x978</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x990</load_address>
         <run_address>0x990</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_ranges</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_ranges</name>
         <load_address>0xb58</load_address>
         <run_address>0xb58</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_ranges</name>
         <load_address>0xb70</load_address>
         <run_address>0xb70</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-151">
         <name>.debug_ranges</name>
         <load_address>0xb98</load_address>
         <run_address>0xb98</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-1f4">
         <name>.debug_ranges</name>
         <load_address>0xbd0</load_address>
         <run_address>0xbd0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_ranges</name>
         <load_address>0xbe8</load_address>
         <run_address>0xbe8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_ranges</name>
         <load_address>0xc10</load_address>
         <run_address>0xc10</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6f7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_str</name>
         <load_address>0x6f7</load_address>
         <run_address>0x6f7</run_address>
         <size>0x2ec0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x35b7</load_address>
         <run_address>0x35b7</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_str</name>
         <load_address>0x371d</load_address>
         <run_address>0x371d</run_address>
         <size>0x17d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_str</name>
         <load_address>0x389a</load_address>
         <run_address>0x389a</run_address>
         <size>0x52e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_str</name>
         <load_address>0x3dc8</load_address>
         <run_address>0x3dc8</run_address>
         <size>0x6fd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_str</name>
         <load_address>0x44c5</load_address>
         <run_address>0x44c5</run_address>
         <size>0x56a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x4a2f</load_address>
         <run_address>0x4a2f</run_address>
         <size>0x259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_str</name>
         <load_address>0x4c88</load_address>
         <run_address>0x4c88</run_address>
         <size>0x241</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1e8">
         <name>.debug_str</name>
         <load_address>0x4ec9</load_address>
         <run_address>0x4ec9</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1ec">
         <name>.debug_str</name>
         <load_address>0x5041</load_address>
         <run_address>0x5041</run_address>
         <size>0x8ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_str</name>
         <load_address>0x58fb</load_address>
         <run_address>0x58fb</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1ee">
         <name>.debug_str</name>
         <load_address>0x76d2</load_address>
         <run_address>0x76d2</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_str</name>
         <load_address>0x83c0</load_address>
         <run_address>0x83c0</run_address>
         <size>0x164</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x8524</load_address>
         <run_address>0x8524</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_str</name>
         <load_address>0x8749</load_address>
         <run_address>0x8749</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_str</name>
         <load_address>0x8a78</load_address>
         <run_address>0x8a78</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_str</name>
         <load_address>0x8b6d</load_address>
         <run_address>0x8b6d</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x8d08</load_address>
         <run_address>0x8d08</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x8e70</load_address>
         <run_address>0x8e70</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.debug_str</name>
         <load_address>0x9045</load_address>
         <run_address>0x9045</run_address>
         <size>0x8f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_str</name>
         <load_address>0x993e</load_address>
         <run_address>0x993e</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1f1">
         <name>.debug_str</name>
         <load_address>0x9a86</load_address>
         <run_address>0x9a86</run_address>
         <size>0x127</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.debug_str</name>
         <load_address>0x9bad</load_address>
         <run_address>0x9bad</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-22a">
         <name>.debug_str</name>
         <load_address>0x9c96</load_address>
         <run_address>0x9c96</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_frame</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x4c8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x540</load_address>
         <run_address>0x540</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_frame</name>
         <load_address>0x570</load_address>
         <run_address>0x570</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x5b0</load_address>
         <run_address>0x5b0</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_frame</name>
         <load_address>0x624</load_address>
         <run_address>0x624</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_frame</name>
         <load_address>0x6b0</load_address>
         <run_address>0x6b0</run_address>
         <size>0xe0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_frame</name>
         <load_address>0x790</load_address>
         <run_address>0x790</run_address>
         <size>0x144</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0x8d4</load_address>
         <run_address>0x8d4</run_address>
         <size>0x148</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_frame</name>
         <load_address>0xa1c</load_address>
         <run_address>0xa1c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_frame</name>
         <load_address>0xa3c</load_address>
         <run_address>0xa3c</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_frame</name>
         <load_address>0xb68</load_address>
         <run_address>0xb68</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_frame</name>
         <load_address>0xf70</load_address>
         <run_address>0xf70</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_frame</name>
         <load_address>0x1128</load_address>
         <run_address>0x1128</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x1180</load_address>
         <run_address>0x1180</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_frame</name>
         <load_address>0x1210</load_address>
         <run_address>0x1210</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0x1310</load_address>
         <run_address>0x1310</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_frame</name>
         <load_address>0x1330</load_address>
         <run_address>0x1330</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x1368</load_address>
         <run_address>0x1368</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x1390</load_address>
         <run_address>0x1390</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_frame</name>
         <load_address>0x13c0</load_address>
         <run_address>0x13c0</run_address>
         <size>0x480</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_frame</name>
         <load_address>0x1840</load_address>
         <run_address>0x1840</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.debug_frame</name>
         <load_address>0x1870</load_address>
         <run_address>0x1870</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_frame</name>
         <load_address>0x189c</load_address>
         <run_address>0x189c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-1f7">
         <name>.debug_frame</name>
         <load_address>0x18bc</load_address>
         <run_address>0x18bc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x34f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_line</name>
         <load_address>0x34f</load_address>
         <run_address>0x34f</run_address>
         <size>0xc88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0xfd7</load_address>
         <run_address>0xfd7</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_line</name>
         <load_address>0x108f</load_address>
         <run_address>0x108f</run_address>
         <size>0x1ae</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x123d</load_address>
         <run_address>0x123d</run_address>
         <size>0x2f8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_line</name>
         <load_address>0x1535</load_address>
         <run_address>0x1535</run_address>
         <size>0x377</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_line</name>
         <load_address>0x18ac</load_address>
         <run_address>0x18ac</run_address>
         <size>0x4e3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x1d8f</load_address>
         <run_address>0x1d8f</run_address>
         <size>0x2ca</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-b"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_line</name>
         <load_address>0x2059</load_address>
         <run_address>0x2059</run_address>
         <size>0x40d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.debug_line</name>
         <load_address>0x2466</load_address>
         <run_address>0x2466</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.debug_line</name>
         <load_address>0x25df</load_address>
         <run_address>0x25df</run_address>
         <size>0x683</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x2c62</load_address>
         <run_address>0x2c62</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.debug_line</name>
         <load_address>0x43d1</load_address>
         <run_address>0x43d1</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_line</name>
         <load_address>0x4de9</load_address>
         <run_address>0x4de9</run_address>
         <size>0x111</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x4efa</load_address>
         <run_address>0x4efa</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_line</name>
         <load_address>0x50d6</load_address>
         <run_address>0x50d6</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_line</name>
         <load_address>0x55f0</load_address>
         <run_address>0x55f0</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x562e</load_address>
         <run_address>0x562e</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x572c</load_address>
         <run_address>0x572c</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x57ec</load_address>
         <run_address>0x57ec</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_line</name>
         <load_address>0x59b4</load_address>
         <run_address>0x59b4</run_address>
         <size>0x1c90</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_line</name>
         <load_address>0x7644</load_address>
         <run_address>0x7644</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.debug_line</name>
         <load_address>0x76ab</load_address>
         <run_address>0x76ab</run_address>
         <size>0xcf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_line</name>
         <load_address>0x777a</load_address>
         <run_address>0x777a</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_line</name>
         <load_address>0x77bb</load_address>
         <run_address>0x77bb</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_line</name>
         <load_address>0x78c2</load_address>
         <run_address>0x78c2</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_line</name>
         <load_address>0x7a27</load_address>
         <run_address>0x7a27</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_line</name>
         <load_address>0x7b33</load_address>
         <run_address>0x7b33</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_line</name>
         <load_address>0x7bec</load_address>
         <run_address>0x7bec</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0x7ccc</load_address>
         <run_address>0x7ccc</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_line</name>
         <load_address>0x7da8</load_address>
         <run_address>0x7da8</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_line</name>
         <load_address>0x7eca</load_address>
         <run_address>0x7eca</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_line</name>
         <load_address>0x7f82</load_address>
         <run_address>0x7f82</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_line</name>
         <load_address>0x8042</load_address>
         <run_address>0x8042</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x80f6</load_address>
         <run_address>0x80f6</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_line</name>
         <load_address>0x81b2</load_address>
         <run_address>0x81b2</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_line</name>
         <load_address>0x8266</load_address>
         <run_address>0x8266</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x832d</load_address>
         <run_address>0x832d</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x83d1</load_address>
         <run_address>0x83d1</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.debug_line</name>
         <load_address>0x848b</load_address>
         <run_address>0x848b</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_line</name>
         <load_address>0x854d</load_address>
         <run_address>0x854d</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-1f5">
         <name>.debug_line</name>
         <load_address>0x8651</load_address>
         <run_address>0x8651</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0x8706</load_address>
         <run_address>0x8706</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_line</name>
         <load_address>0x87a6</load_address>
         <run_address>0x87a6</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-1e9">
         <name>.debug_loc</name>
         <load_address>0x1d</load_address>
         <run_address>0x1d</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.debug_loc</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_loc</name>
         <load_address>0x382</load_address>
         <run_address>0x382</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_loc</name>
         <load_address>0x1da9</load_address>
         <run_address>0x1da9</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_loc</name>
         <load_address>0x2565</load_address>
         <run_address>0x2565</run_address>
         <size>0x136</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x269b</load_address>
         <run_address>0x269b</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_loc</name>
         <load_address>0x2773</load_address>
         <run_address>0x2773</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_loc</name>
         <load_address>0x2b97</load_address>
         <run_address>0x2b97</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x2d03</load_address>
         <run_address>0x2d03</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x2d72</load_address>
         <run_address>0x2d72</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_loc</name>
         <load_address>0x2ed9</load_address>
         <run_address>0x2ed9</run_address>
         <size>0x32d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_loc</name>
         <load_address>0x61b1</load_address>
         <run_address>0x61b1</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1f2">
         <name>.debug_loc</name>
         <load_address>0x61d7</load_address>
         <run_address>0x61d7</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-22b">
         <name>.debug_loc</name>
         <load_address>0x6296</load_address>
         <run_address>0x6296</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-105"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_aranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10e"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_aranges</name>
         <load_address>0x180</load_address>
         <run_address>0x180</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x1a0</load_address>
         <run_address>0x1a0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_aranges</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.debug_aranges</name>
         <load_address>0x1e8</load_address>
         <run_address>0x1e8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_aranges</name>
         <load_address>0x208</load_address>
         <run_address>0x208</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_aranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_aranges</name>
         <load_address>0x260</load_address>
         <run_address>0x260</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2098</size>
         <contents>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-1f3"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-1ef"/>
            <object_component_ref idref="oc-1ea"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x21c0</load_address>
         <run_address>0x21c0</run_address>
         <size>0x60</size>
         <contents>
            <object_component_ref idref="oc-287"/>
            <object_component_ref idref="oc-285"/>
            <object_component_ref idref="oc-288"/>
            <object_component_ref idref="oc-286"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x2158</load_address>
         <run_address>0x2158</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-1a4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-24d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202001b8</run_address>
         <size>0x13d</size>
         <contents>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-b4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1b5</size>
         <contents>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-b2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-28a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-244" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-245" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-246" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-247" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-248" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-249" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-24b" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-267" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d23</size>
         <contents>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-1e7"/>
            <object_component_ref idref="oc-1eb"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-1ed"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-1f0"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-1e6"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-1f8"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-229"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-28c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-269" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x110ba</size>
         <contents>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-1f6"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-28b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-26b" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc38</size>
         <contents>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-1f4"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-11c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-26d" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9e29</size>
         <contents>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-1e8"/>
            <object_component_ref idref="oc-1ec"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-1ee"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-1f1"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-22a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-26f" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18ec</size>
         <contents>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-1f7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-271" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8826</size>
         <contents>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-1f5"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-11d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-273" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x62b6</size>
         <contents>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-1e9"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-1f2"/>
            <object_component_ref idref="oc-22b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-27f" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x288</size>
         <contents>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-11b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-289" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-29a" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2220</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-29b" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x2f5</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-29c" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x2220</used_space>
         <unused_space>0x5de0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2098</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2158</start_address>
               <size>0x68</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x21c0</start_address>
               <size>0x60</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2220</start_address>
               <size>0x5de0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x4f2</used_space>
         <unused_space>0x3b0e</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-249"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-24b"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1b5</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001b5</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x202001b8</start_address>
               <size>0x13d</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202002f5</start_address>
               <size>0x3b0b</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x21c0</load_address>
            <load_size>0x3a</load_size>
            <run_address>0x202001b8</run_address>
            <run_size>0x13d</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x2208</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1b5</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x2210</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x2220</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x2220</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x21fc</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x2208</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4c">
         <name>main</name>
         <value>0x17c5</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-119">
         <name>SYSCFG_DL_init</name>
         <value>0x198d</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-11a">
         <name>SYSCFG_DL_initPower</name>
         <value>0x11b9</value>
         <object_component_ref idref="oc-11f"/>
      </symbol>
      <symbol id="sm-11b">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x6c1</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-11c">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1a45</value>
         <object_component_ref idref="oc-121"/>
      </symbol>
      <symbol id="sm-11d">
         <name>SYSCFG_DL_PWM_MOTOR_init</name>
         <value>0xbb1</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-11e">
         <name>SYSCFG_DL_TIMER_1_init</name>
         <value>0x1711</value>
         <object_component_ref idref="oc-123"/>
      </symbol>
      <symbol id="sm-11f">
         <name>SYSCFG_DL_I2C_Gray_init</name>
         <value>0x12b1</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-120">
         <name>SYSCFG_DL_UART_OPENMV_init</name>
         <value>0x1509</value>
         <object_component_ref idref="oc-125"/>
      </symbol>
      <symbol id="sm-121">
         <name>gPWM_MOTORBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-122">
         <name>gTIMER_1Backup</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-12d">
         <name>Default_Handler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12e">
         <name>Reset_Handler</name>
         <value>0x214b</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-12f">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-130">
         <name>NMI_Handler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-131">
         <name>HardFault_Handler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-132">
         <name>SVC_Handler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-133">
         <name>PendSV_Handler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-134">
         <name>SysTick_Handler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-135">
         <name>GROUP0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-136">
         <name>GROUP1_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-137">
         <name>TIMG8_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-138">
         <name>UART3_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-139">
         <name>ADC0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13a">
         <name>ADC1_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13b">
         <name>CANFD0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13c">
         <name>DAC0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13d">
         <name>SPI0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13e">
         <name>SPI1_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13f">
         <name>UART1_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-140">
         <name>UART2_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-141">
         <name>UART0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-142">
         <name>TIMG0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-143">
         <name>TIMG6_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-144">
         <name>TIMA0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>TIMG7_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-146">
         <name>TIMG12_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-147">
         <name>I2C0_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>I2C1_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>AES_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>RTC_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>DMA_IRQHandler</name>
         <value>0x2143</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-157">
         <name>Gray_Task</name>
         <value>0xc8d</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-158">
         <name>Digtal</name>
         <value>0x202001b4</value>
      </symbol>
      <symbol id="sm-159">
         <name>Ch</name>
         <value>0x202001b8</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-15a">
         <name>g_line_position_error</name>
         <value>0x202002f0</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-15b">
         <name>gray_weights</name>
         <value>0x202002b8</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-170">
         <name>PID_Init</name>
         <value>0x1959</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-171">
         <name>pid_params_line</name>
         <value>0x202002d8</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-172">
         <name>pid_line</name>
         <value>0x20200178</value>
      </symbol>
      <symbol id="sm-173">
         <name>PID_Task</name>
         <value>0xe3d</value>
         <object_component_ref idref="oc-61"/>
      </symbol>
      <symbol id="sm-174">
         <name>pid_running</name>
         <value>0x202002f4</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-175">
         <name>basic_speed</name>
         <value>0x202002ec</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-176">
         <name>TIMA1_IRQHandler</name>
         <value>0x1f81</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-18a">
         <name>Motor_On</name>
         <value>0x1bad</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-18b">
         <name>Set_Speed</name>
         <value>0x345</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-1a5">
         <name>hardware_IIC_ReadByte</name>
         <value>0xef1</value>
         <object_component_ref idref="oc-13c"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>IIC_ReadByte</name>
         <value>0x1d6d</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-1b2">
         <name>IIC_Get_Digtal</name>
         <value>0x1f69</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>pid_init</name>
         <value>0x1425</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-1c8">
         <name>pid_set_target</name>
         <value>0x20e5</value>
         <object_component_ref idref="oc-12b"/>
      </symbol>
      <symbol id="sm-1c9">
         <name>pid_calculate_positional</name>
         <value>0x1bf3</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-1ca">
         <name>pid_constrain</name>
         <value>0x161d</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-1cb">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cc">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cd">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1ce">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cf">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d0">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d1">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d2">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d3">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1dc">
         <name>DL_Common_delayCycles</name>
         <value>0x211f</value>
         <object_component_ref idref="oc-168"/>
      </symbol>
      <symbol id="sm-1eb">
         <name>DL_I2C_setClockConfig</name>
         <value>0x1b87</value>
         <object_component_ref idref="oc-189"/>
      </symbol>
      <symbol id="sm-1ec">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x1377</value>
         <object_component_ref idref="oc-1b1"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>DL_I2C_flushControllerTXFIFO</name>
         <value>0x1699</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-209">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1d35</value>
         <object_component_ref idref="oc-17b"/>
      </symbol>
      <symbol id="sm-20a">
         <name>DL_Timer_initTimerMode</name>
         <value>0x9e5</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-20b">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x20d5</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-20c">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x1d19</value>
         <object_component_ref idref="oc-17f"/>
      </symbol>
      <symbol id="sm-20d">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1f09</value>
         <object_component_ref idref="oc-17e"/>
      </symbol>
      <symbol id="sm-20e">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x8e1</value>
         <object_component_ref idref="oc-17c"/>
      </symbol>
      <symbol id="sm-21b">
         <name>DL_UART_init</name>
         <value>0x14c1</value>
         <object_component_ref idref="oc-19f"/>
      </symbol>
      <symbol id="sm-21c">
         <name>DL_UART_setClockConfig</name>
         <value>0x207d</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-22d">
         <name>sprintf</name>
         <value>0x18ed</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-238">
         <name>_c_int00_noargs</name>
         <value>0x1b39</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-239">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-245">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1801</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-24d">
         <name>_system_pre_init</name>
         <value>0x214f</value>
         <object_component_ref idref="oc-6d"/>
      </symbol>
      <symbol id="sm-258">
         <name>__TI_zero_init_nomemset</name>
         <value>0x1fc7</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-261">
         <name>__TI_decompress_none</name>
         <value>0x20a1</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-26c">
         <name>__TI_decompress_lzss</name>
         <value>0x1235</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-282">
         <name>__TI_printfi_minimal</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-290">
         <name>abort</name>
         <value>0x213d</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-29f">
         <name>memccpy</name>
         <value>0x1bd1</value>
         <object_component_ref idref="oc-1bb"/>
      </symbol>
      <symbol id="sm-2af">
         <name>HOSTexit</name>
         <value>0x2147</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-2b0">
         <name>C$$EXIT</name>
         <value>0x2146</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-2c5">
         <name>__aeabi_fadd</name>
         <value>0xd6f</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-2c6">
         <name>__addsf3</name>
         <value>0xd6f</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-2c7">
         <name>__aeabi_fsub</name>
         <value>0xd65</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-2c8">
         <name>__subsf3</name>
         <value>0xd65</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-2ce">
         <name>__aeabi_dadd</name>
         <value>0x537</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-2cf">
         <name>__adddf3</name>
         <value>0x537</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-2d0">
         <name>__aeabi_dsub</name>
         <value>0x52d</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-2d1">
         <name>__subdf3</name>
         <value>0x52d</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-2d7">
         <name>__aeabi_dmul</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-2d8">
         <name>__muldf3</name>
         <value>0xacd</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-2de">
         <name>__muldsi3</name>
         <value>0x1879</value>
         <object_component_ref idref="oc-156"/>
      </symbol>
      <symbol id="sm-2e4">
         <name>__aeabi_fmul</name>
         <value>0xfa1</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-2e5">
         <name>__mulsf3</name>
         <value>0xfa1</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-2eb">
         <name>__aeabi_fdiv</name>
         <value>0x1135</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-2ec">
         <name>__divsf3</name>
         <value>0x1135</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-2f2">
         <name>__aeabi_ddiv</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-2f3">
         <name>__divdf3</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-2f9">
         <name>__aeabi_f2iz</name>
         <value>0x18b5</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-2fa">
         <name>__fixsfsi</name>
         <value>0x18b5</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-300">
         <name>__aeabi_d2uiz</name>
         <value>0x1599</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-301">
         <name>__fixunsdfsi</name>
         <value>0x1599</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-307">
         <name>__aeabi_i2d</name>
         <value>0x1a19</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-308">
         <name>__floatsidf</name>
         <value>0x1a19</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-30e">
         <name>__aeabi_i2f</name>
         <value>0x174d</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-30f">
         <name>__floatsisf</name>
         <value>0x174d</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-315">
         <name>__aeabi_ui2f</name>
         <value>0x1b11</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-316">
         <name>__floatunsisf</name>
         <value>0x1b11</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-31c">
         <name>__aeabi_fcmpeq</name>
         <value>0x1315</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-31d">
         <name>__aeabi_fcmplt</name>
         <value>0x1329</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-31e">
         <name>__aeabi_fcmple</name>
         <value>0x133d</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-31f">
         <name>__aeabi_fcmpge</name>
         <value>0x1351</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-320">
         <name>__aeabi_fcmpgt</name>
         <value>0x1365</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-326">
         <name>__aeabi_memcpy</name>
         <value>0x2135</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-327">
         <name>__aeabi_memcpy4</name>
         <value>0x2135</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-328">
         <name>__aeabi_memcpy8</name>
         <value>0x2135</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-32f">
         <name>__aeabi_memset</name>
         <value>0x20f5</value>
         <object_component_ref idref="oc-1ba"/>
      </symbol>
      <symbol id="sm-330">
         <name>__aeabi_memset4</name>
         <value>0x20f5</value>
         <object_component_ref idref="oc-1ba"/>
      </symbol>
      <symbol id="sm-331">
         <name>__aeabi_memset8</name>
         <value>0x20f5</value>
         <object_component_ref idref="oc-1ba"/>
      </symbol>
      <symbol id="sm-337">
         <name>__aeabi_uidiv</name>
         <value>0x15dd</value>
         <object_component_ref idref="oc-1c0"/>
      </symbol>
      <symbol id="sm-338">
         <name>__aeabi_uidivmod</name>
         <value>0x15dd</value>
         <object_component_ref idref="oc-1c0"/>
      </symbol>
      <symbol id="sm-341">
         <name>__eqsf2</name>
         <value>0x183d</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-342">
         <name>__lesf2</name>
         <value>0x183d</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-343">
         <name>__ltsf2</name>
         <value>0x183d</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-344">
         <name>__nesf2</name>
         <value>0x183d</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-345">
         <name>__cmpsf2</name>
         <value>0x183d</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-346">
         <name>__gtsf2</name>
         <value>0x1789</value>
         <object_component_ref idref="oc-152"/>
      </symbol>
      <symbol id="sm-347">
         <name>__gesf2</name>
         <value>0x1789</value>
         <object_component_ref idref="oc-152"/>
      </symbol>
      <symbol id="sm-352">
         <name>__aeabi_idiv0</name>
         <value>0x6bf</value>
         <object_component_ref idref="oc-1f3"/>
      </symbol>
      <symbol id="sm-35c">
         <name>TI_memcpy_small</name>
         <value>0x208f</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-365">
         <name>TI_memset_small</name>
         <value>0x2111</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-366">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-369">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-36a">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
