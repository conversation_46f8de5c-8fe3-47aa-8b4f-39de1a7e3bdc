#include "ti_msp_dl_config.h"

// // TB6612 电机配置结构体
// typedef struct MOTOR_Config{
//     GPTIMER_Regs *gptimer; // 定时器
//     DL_TIMER_CC_INDEX ccIndex; // PWM 通道

//         GPIO_Regs *gpio;//GPIO端口
//         uint32_t in1_pin;// 对应 TB6612 中的 AIN1
//         uint32_t in2_pin;
//     unsigned char reverse; // 电机的方向是否反转。0-正常，1-反转
// } MOTOR_Config;


// // 电机结构体
// typedef struct MOTOR
// {
//     MOTOR_Config config;
//     int speed;
//     int dead_band_speed; // 死区（当 -dead_band_speed < speed < dead_band_speed 时电机不会转动）
// } MOTOR;



// // // 电机初始化函数(绑定相关的功能引脚)
// // void Motor_Config_Init(MOTOR* motor, TIM_HandleTypeDef *htim, uint32_t pwm_channel, 
// //                        GPIO_TypeDef *in1_port, uint16_t in1_pin, GPIO_TypeDef *in2_port, uint16_t in2_pin, 
// //                        unsigned char reverse, int dead_band_speed);

// // // 速度控制
// // void Motor_Set_Speed(MOTOR* motor, int speed);

// // // 电机停止  
// // void Motor_Stop(MOTOR* motor);          

// // // 电机刹车
// // void Motor_Brake(MOTOR* motor);   


void Motor_On(void);
void Motor_Off(void);
void Set_Speed(uint8_t side, int8_t duty);