******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 20:28:49 2025

OUTPUT FILE NAME:   <my_project.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000fcd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00001400  00006c00  R  X
  SRAM                  20200000   00004000  000002c8  00003d38  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001400   00001400    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000012f0   000012f0    r-x .text
  000013b0    000013b0    00000018   00000018    r-- .rodata
  000013c8    000013c8    00000038   00000038    r-- .cinit
20200000    20200000    000000c8   00000000    rw-
  20200000    20200000    000000bc   00000000    rw- .bss
  202000bc    202000bc    0000000c   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000012f0     
                  000000c0    000001cc     motor.o (.text.Set_Speed)
                  0000028c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000041e    00000002     --HOLE-- [fill = 0]
                  00000420    0000010c                            : divdf3.S.obj (.text.__divdf3)
                  0000052c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000630    000000f8     empty.o (.text.Motor_Ctrl)
                  00000728    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000080c    000000e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000008ec    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  000009c8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000aa0    0000008c                            : mulsf3.S.obj (.text.__mulsf3)
                  00000b2c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000ba8    00000078     empty.o (.text.main)
                  00000c20    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000c78    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00000cc4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000d0c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_OPENMV_init)
                  00000d54    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00000d96    00000002     --HOLE-- [fill = 0]
                  00000d98    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00000dd4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00000e10    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000e4c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00000e86    00000002     --HOLE-- [fill = 0]
                  00000e88    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00000ec0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00000ef4    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00000f20    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00000f4c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00000f78    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000fa2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000fca    00000002     --HOLE-- [fill = 0]
                  00000fcc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000ff4    00000024     motor.o (.text.Motor_On)
                  00001018    00000024     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000103c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000105c    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  0000107c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000109a    00000002     --HOLE-- [fill = 0]
                  0000109c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000010b8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  000010d4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000010f0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000110c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001128    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001144    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001160    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001178    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001190    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000011a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000011c0    00000018     motor.o (.text.DL_GPIO_setPins)
                  000011d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000011f0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001208    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001220    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001238    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001250    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001268    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001280    00000016     empty.o (.text.DL_GPIO_readPins)
                  00001296    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000012ac    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000012c2    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000012d6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000012ea    00000002     --HOLE-- [fill = 0]
                  000012ec    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001300    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001314    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00001328    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000133a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000134c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000135e    00000002     --HOLE-- [fill = 0]
                  00001360    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001370    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001380    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  0000138a    00000002     --HOLE-- [fill = 0]
                  0000138c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001394    00000006     libc.a : exit.c.obj (.text:abort)
                  0000139a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000139e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000013a2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000013a6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000013aa    00000006     --HOLE-- [fill = 0]

.cinit     0    000013c8    00000038     
                  000013c8    00000010     (.cinit..data.load) [load image, compression = lzss]
                  000013d8    0000000c     (__TI_handler_table)
                  000013e4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000013ec    00000010     (__TI_cinit_table)
                  000013fc    00000004     --HOLE-- [fill = 0]

.rodata    0    000013b0    00000018     
                  000013b0    0000000a     ti_msp_dl_config.o (.rodata.gUART_OPENMVConfig)
                  000013ba    00000002     ti_msp_dl_config.o (.rodata.gUART_OPENMVClockConfig)
                  000013bc    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  000013c4    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  000013c7    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000bc     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_MOTORBackup)

.data      0    202000bc    0000000c     UNINITIALIZED
                  202000bc    00000004     empty.o (.data.Kp)
                  202000c0    00000002     empty.o (.data.SpeedL)
                  202000c2    00000002     empty.o (.data.SpeedR)
                  202000c4    00000002     empty.o (.data.baseSpeed)
                  202000c6    00000002     empty.o (.data.cx)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1470   23        188    
       empty.o                        478    0         12     
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         1956   215       200    
                                                              
    .\Driver\
       motor.o                        540    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         540    0         0      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_uart.o                      122    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         488    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       divdf3.S.obj                   268    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       fixunsdfsi.S.obj               66     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatsidf.S.obj                44     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1546   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      52        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4826   267       712    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000013ec records: 2, size/record: 8, table size: 16
	.data: load addr=000013c8, load size=00000010 bytes, run addr=202000bc, run size=0000000c bytes, compression=lzss
	.bss: load addr=000013e4, load size=00000008 bytes, run addr=20200000, run size=000000bc bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000013d8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000139b  ADC0_IRQHandler                 
0000139b  ADC1_IRQHandler                 
0000139b  AES_IRQHandler                  
0000139e  C$$EXIT                         
0000139b  CANFD0_IRQHandler               
0000139b  DAC0_IRQHandler                 
00001381  DL_Common_delayCycles           
0000052d  DL_Timer_initFourCCPWMMode      
0000110d  DL_Timer_setCaptCompUpdateMethod
00001239  DL_Timer_setCaptureCompareOutCtl
00001371  DL_Timer_setCaptureCompareValue 
00001129  DL_Timer_setClockConfig         
00000cc5  DL_UART_init                    
00001329  DL_UART_setClockConfig          
0000105d  DL_UART_transmitDataBlocking    
0000139b  DMA_IRQHandler                  
0000139b  Default_Handler                 
0000139b  GROUP0_IRQHandler               
0000139b  GROUP1_IRQHandler               
0000139f  HOSTexit                        
0000139b  HardFault_Handler               
0000139b  I2C0_IRQHandler                 
0000139b  I2C1_IRQHandler                 
202000bc  Kp                              
00000631  Motor_Ctrl                      
00000ff5  Motor_On                        
0000139b  NMI_Handler                     
0000139b  PendSV_Handler                  
0000139b  RTC_IRQHandler                  
000013a3  Reset_Handler                   
0000139b  SPI0_IRQHandler                 
0000139b  SPI1_IRQHandler                 
0000139b  SVC_Handler                     
0000080d  SYSCFG_DL_GPIO_init             
000008ed  SYSCFG_DL_PWM_MOTOR_init        
00000f79  SYSCFG_DL_SYSCTL_init           
00000d0d  SYSCFG_DL_UART_OPENMV_init      
00001019  SYSCFG_DL_init                  
00000c21  SYSCFG_DL_initPower             
000000c1  Set_Speed                       
202000c0  SpeedL                          
202000c2  SpeedR                          
0000139b  SysTick_Handler                 
0000139b  TIMA0_IRQHandler                
0000139b  TIMA1_IRQHandler                
0000139b  TIMG0_IRQHandler                
0000139b  TIMG12_IRQHandler               
0000139b  TIMG6_IRQHandler                
0000139b  TIMG7_IRQHandler                
0000139b  TIMG8_IRQHandler                
0000133b  TI_memcpy_small                 
0000139b  UART0_IRQHandler                
0000139b  UART1_IRQHandler                
0000139b  UART2_IRQHandler                
0000139b  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000013ec  __TI_CINIT_Base                 
000013fc  __TI_CINIT_Limit                
000013fc  __TI_CINIT_Warm                 
000013d8  __TI_Handler_Table_Base         
000013e4  __TI_Handler_Table_Limit        
00000e11  __TI_auto_init_nobinit_nopinit  
00000b2d  __TI_decompress_lzss            
0000134d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000012ad  __TI_zero_init_nomemset         
00000297  __adddf3                        
000009d3  __addsf3                        
00000d55  __aeabi_d2uiz                   
00000297  __aeabi_dadd                    
00000421  __aeabi_ddiv                    
00000729  __aeabi_dmul                    
0000028d  __aeabi_dsub                    
00000e89  __aeabi_f2iz                    
000009d3  __aeabi_fadd                    
00000aa1  __aeabi_fmul                    
000009c9  __aeabi_fsub                    
00000f4d  __aeabi_i2d                     
00000dd5  __aeabi_i2f                     
0000138d  __aeabi_memcpy                  
0000138d  __aeabi_memcpy4                 
0000138d  __aeabi_memcpy8                 
ffffffff  __binit__                       
00000421  __divdf3                        
00000e89  __fixsfsi                       
00000d55  __fixunsdfsi                    
00000f4d  __floatsidf                     
00000dd5  __floatsisf                     
UNDEFED   __mpu_init                      
00000729  __muldf3                        
00000e4d  __muldsi3                       
00000aa1  __mulsf3                        
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
0000028d  __subdf3                        
000009c9  __subsf3                        
00000fcd  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000013a7  _system_pre_init                
00001395  abort                           
202000c4  baseSpeed                       
ffffffff  binit                           
202000c6  cx                              
20200000  gPWM_MOTORBackup                
00000000  interruptVectors                
00000ba9  main                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  Set_Speed                       
00000200  __STACK_SIZE                    
0000028d  __aeabi_dsub                    
0000028d  __subdf3                        
00000297  __adddf3                        
00000297  __aeabi_dadd                    
00000421  __aeabi_ddiv                    
00000421  __divdf3                        
0000052d  DL_Timer_initFourCCPWMMode      
00000631  Motor_Ctrl                      
00000729  __aeabi_dmul                    
00000729  __muldf3                        
0000080d  SYSCFG_DL_GPIO_init             
000008ed  SYSCFG_DL_PWM_MOTOR_init        
000009c9  __aeabi_fsub                    
000009c9  __subsf3                        
000009d3  __addsf3                        
000009d3  __aeabi_fadd                    
00000aa1  __aeabi_fmul                    
00000aa1  __mulsf3                        
00000b2d  __TI_decompress_lzss            
00000ba9  main                            
00000c21  SYSCFG_DL_initPower             
00000cc5  DL_UART_init                    
00000d0d  SYSCFG_DL_UART_OPENMV_init      
00000d55  __aeabi_d2uiz                   
00000d55  __fixunsdfsi                    
00000dd5  __aeabi_i2f                     
00000dd5  __floatsisf                     
00000e11  __TI_auto_init_nobinit_nopinit  
00000e4d  __muldsi3                       
00000e89  __aeabi_f2iz                    
00000e89  __fixsfsi                       
00000f4d  __aeabi_i2d                     
00000f4d  __floatsidf                     
00000f79  SYSCFG_DL_SYSCTL_init           
00000fcd  _c_int00_noargs                 
00000ff5  Motor_On                        
00001019  SYSCFG_DL_init                  
0000105d  DL_UART_transmitDataBlocking    
0000110d  DL_Timer_setCaptCompUpdateMethod
00001129  DL_Timer_setClockConfig         
00001239  DL_Timer_setCaptureCompareOutCtl
000012ad  __TI_zero_init_nomemset         
00001329  DL_UART_setClockConfig          
0000133b  TI_memcpy_small                 
0000134d  __TI_decompress_none            
00001371  DL_Timer_setCaptureCompareValue 
00001381  DL_Common_delayCycles           
0000138d  __aeabi_memcpy                  
0000138d  __aeabi_memcpy4                 
0000138d  __aeabi_memcpy8                 
00001395  abort                           
0000139b  ADC0_IRQHandler                 
0000139b  ADC1_IRQHandler                 
0000139b  AES_IRQHandler                  
0000139b  CANFD0_IRQHandler               
0000139b  DAC0_IRQHandler                 
0000139b  DMA_IRQHandler                  
0000139b  Default_Handler                 
0000139b  GROUP0_IRQHandler               
0000139b  GROUP1_IRQHandler               
0000139b  HardFault_Handler               
0000139b  I2C0_IRQHandler                 
0000139b  I2C1_IRQHandler                 
0000139b  NMI_Handler                     
0000139b  PendSV_Handler                  
0000139b  RTC_IRQHandler                  
0000139b  SPI0_IRQHandler                 
0000139b  SPI1_IRQHandler                 
0000139b  SVC_Handler                     
0000139b  SysTick_Handler                 
0000139b  TIMA0_IRQHandler                
0000139b  TIMA1_IRQHandler                
0000139b  TIMG0_IRQHandler                
0000139b  TIMG12_IRQHandler               
0000139b  TIMG6_IRQHandler                
0000139b  TIMG7_IRQHandler                
0000139b  TIMG8_IRQHandler                
0000139b  UART0_IRQHandler                
0000139b  UART1_IRQHandler                
0000139b  UART2_IRQHandler                
0000139b  UART3_IRQHandler                
0000139e  C$$EXIT                         
0000139f  HOSTexit                        
000013a3  Reset_Handler                   
000013a7  _system_pre_init                
000013d8  __TI_Handler_Table_Base         
000013e4  __TI_Handler_Table_Limit        
000013ec  __TI_CINIT_Base                 
000013fc  __TI_CINIT_Limit                
000013fc  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_MOTORBackup                
202000bc  Kp                              
202000c0  SpeedL                          
202000c2  SpeedR                          
202000c4  baseSpeed                       
202000c6  cx                              
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[122 symbols]
