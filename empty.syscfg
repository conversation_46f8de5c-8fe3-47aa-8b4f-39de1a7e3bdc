/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const I2C    = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1   = I2C.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                         = "GPIO_MOTOR";
GPIO1.associatedPins.create(6);
GPIO1.associatedPins[0].$name       = "PIN_BL1";
GPIO1.associatedPins[0].pin.$assign = "PB3";
GPIO1.associatedPins[1].$name       = "PIN_BL2";
GPIO1.associatedPins[1].pin.$assign = "PA17";
GPIO1.associatedPins[2].$name       = "PIN_BR1";
GPIO1.associatedPins[2].pin.$assign = "PA24";
GPIO1.associatedPins[3].$name       = "PIN_BR2";
GPIO1.associatedPins[3].pin.$assign = "PA18";
GPIO1.associatedPins[4].$name       = "PIN_FSTBY";
GPIO1.associatedPins[4].pin.$assign = "PB19";
GPIO1.associatedPins[5].$name       = "PIN_BSTBY";
GPIO1.associatedPins[5].pin.$assign = "PB13";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "GPIO_BUTTON";
GPIO2.associatedPins[0].$name            = "PIN_S2";
GPIO2.associatedPins[0].assignedPort     = "PORTB";
GPIO2.associatedPins[0].assignedPin      = "21";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";

GPIO3.$name                          = "GPIO_LED";
GPIO3.associatedPins[0].$name        = "PIN_LED1";
GPIO3.associatedPins[0].assignedPort = "PORTA";
GPIO3.associatedPins[0].assignedPin  = "0";
GPIO3.associatedPins[0].initialValue = "SET";

I2C1.$name                             = "I2C_Gray";
I2C1.basicEnableController             = true;
I2C1.intController                     = ["ARBITRATION_LOST","NACK","RXFIFO_TRIGGER","RX_DONE","TX_DONE"];
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

PWM1.$name                              = "PWM_MOTOR";
PWM1.timerCount                         = 3200;
PWM1.ccIndex                            = [0,1,2,3];
PWM1.timerStartTimer                    = true;
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.ccValue              = 3199;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.ccValue              = 3199;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM1.peripheral.$assign                 = "TIMA0";
PWM1.peripheral.ccp0Pin.$assign         = "PA8";
PWM1.peripheral.ccp1Pin.$assign         = "PA9";
PWM1.peripheral.ccp2Pin.$assign         = "PB0";
PWM1.peripheral.ccp3Pin.$assign         = "PB2";
PWM1.PWM_CHANNEL_2.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.PWM_CHANNEL_2.ccValue              = 3199;
PWM1.PWM_CHANNEL_3.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM1.PWM_CHANNEL_3.ccValue              = 3199;
PWM1.ccp2PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp3PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

SYSCTL.forceDefaultClkConfig = true;

TIMER1.$name             = "TIMER_1";
TIMER1.timerPeriod       = "5ms";
TIMER1.timerClkPrescale  = 4;
TIMER1.timerMode         = "PERIODIC_UP";
TIMER1.interrupts        = ["ZERO"];
TIMER1.interruptPriority = "0";

UART1.$name                            = "UART_OPENMV";
UART1.enabledInterrupts                = ["RX"];
UART1.peripheral.rxPin.$assign         = "PB7";
UART1.peripheral.txPin.$assign         = "PB6";
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB21";
GPIO3.associatedPins[0].pin.$suggestSolution = "PA0";
I2C1.peripheral.$suggestSolution             = "I2C0";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";
TIMER1.peripheral.$suggestSolution           = "TIMA1";
UART1.peripheral.$suggestSolution            = "UART1";
