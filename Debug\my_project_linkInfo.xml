<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o my_project.out -mmy_project.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/my_project -iC:/Users/<USER>/workspace_ccstheia/my_project/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=my_project_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./Driver/motor.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688a1001</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\my_project\Debug\my_project.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xfcd</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project\Debug\.\Driver\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\my_project\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-2b">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixunsdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e3">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e4">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text.Set_Speed</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x1cc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.adddf3_subdf3</name>
         <load_address>0x28c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x28c</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.__divdf3</name>
         <load_address>0x420</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x420</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x52c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x52c</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.Motor_Ctrl</name>
         <load_address>0x630</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x630</run_address>
         <size>0xf8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.__muldf3</name>
         <load_address>0x728</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x728</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x80c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x80c</run_address>
         <size>0xe0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.SYSCFG_DL_PWM_MOTOR_init</name>
         <load_address>0x8ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8ec</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text</name>
         <load_address>0x9c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c8</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.__mulsf3</name>
         <load_address>0xaa0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaa0</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xb2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb2c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0xba8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba8</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xc20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc20</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0xc78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc78</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.DL_UART_init</name>
         <load_address>0xcc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcc4</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.SYSCFG_DL_UART_OPENMV_init</name>
         <load_address>0xd0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd0c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.__fixunsdfsi</name>
         <load_address>0xd54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd54</run_address>
         <size>0x42</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0xd98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd98</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text.__floatsisf</name>
         <load_address>0xdd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdd4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xe10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe10</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.__muldsi3</name>
         <load_address>0xe4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe4c</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.__fixsfsi</name>
         <load_address>0xe88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe88</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0xec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0xef4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xef4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0xf20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf20</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.__floatsidf</name>
         <load_address>0xf4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf4c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xf78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf78</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0xfa2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfa2</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xfcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfcc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.Motor_On</name>
         <load_address>0xff4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xff4</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1018</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1018</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x103c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x103c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.DL_UART_transmitDataBlocking</name>
         <load_address>0x105c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x105c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x107c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x107c</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x109c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x109c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x10b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10b8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x10d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10d4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-119">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x10f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x110c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x110c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1128</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1128</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x1144</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1144</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1160</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1160</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1178</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1190</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1190</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x11a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11a8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x11c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11c0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x11d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11d8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x11f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1208</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1208</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1220</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1220</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1238</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1238</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1250</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1250</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1268</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1268</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1280</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1280</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1296</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1296</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x12ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12ac</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x12c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12c2</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x12d6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12d6</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x12ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12ec</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1300</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1300</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1314</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1314</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x1328</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1328</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x133a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x133a</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x134c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x134c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x1360</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1360</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x1370</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1370</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1380</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1380</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x138c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x138c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-98">
         <name>.text:abort</name>
         <load_address>0x1394</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1394</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x139a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x139a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.HOSTexit</name>
         <load_address>0x139e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x139e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x13a2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x13a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-189">
         <name>.cinit..data.load</name>
         <load_address>0x13c8</load_address>
         <readonly>true</readonly>
         <run_address>0x13c8</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-187">
         <name>__TI_handler_table</name>
         <load_address>0x13d8</load_address>
         <readonly>true</readonly>
         <run_address>0x13d8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18a">
         <name>.cinit..bss.load</name>
         <load_address>0x13e4</load_address>
         <readonly>true</readonly>
         <run_address>0x13e4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-188">
         <name>__TI_cinit_table</name>
         <load_address>0x13ec</load_address>
         <readonly>true</readonly>
         <run_address>0x13ec</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-12b">
         <name>.rodata.gUART_OPENMVConfig</name>
         <load_address>0x13b0</load_address>
         <readonly>true</readonly>
         <run_address>0x13b0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.rodata.gUART_OPENMVClockConfig</name>
         <load_address>0x13ba</load_address>
         <readonly>true</readonly>
         <run_address>0x13ba</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-123">
         <name>.rodata.gPWM_MOTORConfig</name>
         <load_address>0x13bc</load_address>
         <readonly>true</readonly>
         <run_address>0x13bc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-122">
         <name>.rodata.gPWM_MOTORClockConfig</name>
         <load_address>0x13c4</load_address>
         <readonly>true</readonly>
         <run_address>0x13c4</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f3">
         <name>.data.cx</name>
         <load_address>0x202000c6</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-95">
         <name>.data.baseSpeed</name>
         <load_address>0x202000c4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c4</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.data.SpeedL</name>
         <load_address>0x202000c0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c0</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.data.SpeedR</name>
         <load_address>0x202000c2</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.data.Kp</name>
         <load_address>0x202000bc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.common:gPWM_MOTORBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x164</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_abbrev</name>
         <load_address>0x164</load_address>
         <run_address>0x164</run_address>
         <size>0x1b2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x316</load_address>
         <run_address>0x316</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0x383</load_address>
         <run_address>0x383</run_address>
         <size>0x116</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_abbrev</name>
         <load_address>0x499</load_address>
         <run_address>0x499</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_abbrev</name>
         <load_address>0x4fb</load_address>
         <run_address>0x4fb</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_abbrev</name>
         <load_address>0x781</load_address>
         <run_address>0x781</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0xa1c</load_address>
         <run_address>0xa1c</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_abbrev</name>
         <load_address>0xacb</load_address>
         <run_address>0xacb</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0xc3b</load_address>
         <run_address>0xc3b</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0xc74</load_address>
         <run_address>0xc74</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_abbrev</name>
         <load_address>0xd36</load_address>
         <run_address>0xd36</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0xda6</load_address>
         <run_address>0xda6</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_abbrev</name>
         <load_address>0xe33</load_address>
         <run_address>0xe33</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_abbrev</name>
         <load_address>0xecb</load_address>
         <run_address>0xecb</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_abbrev</name>
         <load_address>0xef7</load_address>
         <run_address>0xef7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_abbrev</name>
         <load_address>0xf1e</load_address>
         <run_address>0xf1e</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0xf45</load_address>
         <run_address>0xf45</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_abbrev</name>
         <load_address>0xf6c</load_address>
         <run_address>0xf6c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_abbrev</name>
         <load_address>0xf93</load_address>
         <run_address>0xf93</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0xfba</load_address>
         <run_address>0xfba</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_abbrev</name>
         <load_address>0xfe1</load_address>
         <run_address>0xfe1</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_abbrev</name>
         <load_address>0x1008</load_address>
         <run_address>0x1008</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_abbrev</name>
         <load_address>0x102f</load_address>
         <run_address>0x102f</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_abbrev</name>
         <load_address>0x1056</load_address>
         <run_address>0x1056</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_abbrev</name>
         <load_address>0x107d</load_address>
         <run_address>0x107d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_abbrev</name>
         <load_address>0x10a4</load_address>
         <run_address>0x10a4</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_abbrev</name>
         <load_address>0x10c9</load_address>
         <run_address>0x10c9</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_info</name>
         <load_address>0xe9a</load_address>
         <run_address>0xe9a</run_address>
         <size>0x28d3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x376d</load_address>
         <run_address>0x376d</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_info</name>
         <load_address>0x37ed</load_address>
         <run_address>0x37ed</run_address>
         <size>0xea3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0x4690</load_address>
         <run_address>0x4690</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_info</name>
         <load_address>0x4705</load_address>
         <run_address>0x4705</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0x7877</load_address>
         <run_address>0x7877</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x8b1d</load_address>
         <run_address>0x8b1d</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0x8f40</load_address>
         <run_address>0x8f40</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_info</name>
         <load_address>0x9684</load_address>
         <run_address>0x9684</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x96ca</load_address>
         <run_address>0x96ca</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x985c</load_address>
         <run_address>0x985c</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0x9922</load_address>
         <run_address>0x9922</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_info</name>
         <load_address>0x9a9e</load_address>
         <run_address>0x9a9e</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_info</name>
         <load_address>0x9b96</load_address>
         <run_address>0x9b96</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_info</name>
         <load_address>0x9bd1</load_address>
         <run_address>0x9bd1</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_info</name>
         <load_address>0x9d78</load_address>
         <run_address>0x9d78</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_info</name>
         <load_address>0x9f1f</load_address>
         <run_address>0x9f1f</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_info</name>
         <load_address>0xa0ac</load_address>
         <run_address>0xa0ac</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_info</name>
         <load_address>0xa23b</load_address>
         <run_address>0xa23b</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_info</name>
         <load_address>0xa3c8</load_address>
         <run_address>0xa3c8</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_info</name>
         <load_address>0xa555</load_address>
         <run_address>0xa555</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_info</name>
         <load_address>0xa6e4</load_address>
         <run_address>0xa6e4</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_info</name>
         <load_address>0xa879</load_address>
         <run_address>0xa879</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_info</name>
         <load_address>0xaa0c</load_address>
         <run_address>0xaa0c</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_info</name>
         <load_address>0xab9f</load_address>
         <run_address>0xab9f</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_info</name>
         <load_address>0xad38</load_address>
         <run_address>0xad38</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.debug_info</name>
         <load_address>0xb032</load_address>
         <run_address>0xb032</run_address>
         <size>0xa6</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_ranges</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x120</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x150</load_address>
         <run_address>0x150</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_ranges</name>
         <load_address>0x198</load_address>
         <run_address>0x198</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_ranges</name>
         <load_address>0x370</load_address>
         <run_address>0x370</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x518</load_address>
         <run_address>0x518</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_ranges</name>
         <load_address>0x560</load_address>
         <run_address>0x560</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_ranges</name>
         <load_address>0x5a8</load_address>
         <run_address>0x5a8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x5c0</load_address>
         <run_address>0x5c0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_ranges</name>
         <load_address>0x610</load_address>
         <run_address>0x610</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_ranges</name>
         <load_address>0x628</load_address>
         <run_address>0x628</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x882</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_str</name>
         <load_address>0x882</load_address>
         <run_address>0x882</run_address>
         <size>0x22f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x2b7b</load_address>
         <run_address>0x2b7b</run_address>
         <size>0x15d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_str</name>
         <load_address>0x2cd8</load_address>
         <run_address>0x2cd8</run_address>
         <size>0x6f4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_str</name>
         <load_address>0x33cc</load_address>
         <run_address>0x33cc</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_str</name>
         <load_address>0x3544</load_address>
         <run_address>0x3544</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_str</name>
         <load_address>0x531b</load_address>
         <run_address>0x531b</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x6009</load_address>
         <run_address>0x6009</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_str</name>
         <load_address>0x622e</load_address>
         <run_address>0x622e</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0x655d</load_address>
         <run_address>0x655d</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x6652</load_address>
         <run_address>0x6652</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_str</name>
         <load_address>0x67ed</load_address>
         <run_address>0x67ed</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x6955</load_address>
         <run_address>0x6955</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_str</name>
         <load_address>0x6b2a</load_address>
         <run_address>0x6b2a</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_str</name>
         <load_address>0x6c72</load_address>
         <run_address>0x6c72</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_frame</name>
         <load_address>0x84</load_address>
         <run_address>0x84</run_address>
         <size>0x338</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x3bc</load_address>
         <run_address>0x3bc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_frame</name>
         <load_address>0x3ec</load_address>
         <run_address>0x3ec</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_frame</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_frame</name>
         <load_address>0x498</load_address>
         <run_address>0x498</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_frame</name>
         <load_address>0x8a0</load_address>
         <run_address>0x8a0</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0xa58</load_address>
         <run_address>0xa58</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0xae8</load_address>
         <run_address>0xae8</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_frame</name>
         <load_address>0xbe8</load_address>
         <run_address>0xbe8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_frame</name>
         <load_address>0xc08</load_address>
         <run_address>0xc08</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0xc40</load_address>
         <run_address>0xc40</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0xc68</load_address>
         <run_address>0xc68</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_frame</name>
         <load_address>0xc98</load_address>
         <run_address>0xc98</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_frame</name>
         <load_address>0xcc8</load_address>
         <run_address>0xcc8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_line</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x8e6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0xc86</load_address>
         <run_address>0xc86</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0xd3e</load_address>
         <run_address>0xd3e</run_address>
         <size>0x370</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0x10ae</load_address>
         <run_address>0x10ae</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_line</name>
         <load_address>0x1227</load_address>
         <run_address>0x1227</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x2996</load_address>
         <run_address>0x2996</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x33ae</load_address>
         <run_address>0x33ae</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x358a</load_address>
         <run_address>0x358a</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0x3aa4</load_address>
         <run_address>0x3aa4</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x3ae2</load_address>
         <run_address>0x3ae2</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x3be0</load_address>
         <run_address>0x3be0</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x3ca0</load_address>
         <run_address>0x3ca0</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_line</name>
         <load_address>0x3e68</load_address>
         <run_address>0x3e68</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_line</name>
         <load_address>0x3ecf</load_address>
         <run_address>0x3ecf</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_line</name>
         <load_address>0x3f10</load_address>
         <run_address>0x3f10</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x4017</load_address>
         <run_address>0x4017</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_line</name>
         <load_address>0x417c</load_address>
         <run_address>0x417c</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_line</name>
         <load_address>0x4288</load_address>
         <run_address>0x4288</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_line</name>
         <load_address>0x4341</load_address>
         <run_address>0x4341</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_line</name>
         <load_address>0x4421</load_address>
         <run_address>0x4421</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_line</name>
         <load_address>0x4543</load_address>
         <run_address>0x4543</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_line</name>
         <load_address>0x45fb</load_address>
         <run_address>0x45fb</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_line</name>
         <load_address>0x46bb</load_address>
         <run_address>0x46bb</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_line</name>
         <load_address>0x476f</load_address>
         <run_address>0x476f</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x482b</load_address>
         <run_address>0x482b</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_line</name>
         <load_address>0x48cf</load_address>
         <run_address>0x48cf</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_loc</name>
         <load_address>0x1a3a</load_address>
         <run_address>0x1a3a</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x21f6</load_address>
         <run_address>0x21f6</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_loc</name>
         <load_address>0x22ce</load_address>
         <run_address>0x22ce</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x26f2</load_address>
         <run_address>0x26f2</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_loc</name>
         <load_address>0x285e</load_address>
         <run_address>0x285e</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_loc</name>
         <load_address>0x28cd</load_address>
         <run_address>0x28cd</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_loc</name>
         <load_address>0x2a34</load_address>
         <run_address>0x2a34</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_aranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x12f0</size>
         <contents>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x13c8</load_address>
         <run_address>0x13c8</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-188"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x13b0</load_address>
         <run_address>0x13b0</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-122"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-151"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000bc</run_address>
         <size>0xc</size>
         <contents>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-18c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-148" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-149" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14a" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14b" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14c" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14d" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14f" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-16b" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10d8</size>
         <contents>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-18e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16d" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb0d8</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-18d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16f" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x650</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-76"/>
         </contents>
      </logical_group>
      <logical_group id="lg-171" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6d5b</size>
         <contents>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-13c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-173" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xce8</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-f8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-175" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x496f</size>
         <contents>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-177" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2a5a</size>
         <contents>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-9e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-181" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x188</size>
         <contents>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-74"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18b" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-19a" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1400</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-19b" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0xc8</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-19c" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x1400</used_space>
         <unused_space>0x6c00</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x12f0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x13b0</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x13c8</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1400</start_address>
               <size>0x6c00</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x2c8</used_space>
         <unused_space>0x3d38</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-14d"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-14f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xbc</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202000bc</start_address>
               <size>0xc</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202000c8</start_address>
               <size>0x3d38</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x13c8</load_address>
            <load_size>0x10</load_size>
            <run_address>0x202000bc</run_address>
            <run_size>0xc</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x13e4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xbc</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x13ec</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x13fc</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x13fc</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x13d8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x13e4</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4a">
         <name>main</name>
         <value>0xba9</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-4b">
         <name>Motor_Ctrl</name>
         <value>0x631</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-4c">
         <name>baseSpeed</name>
         <value>0x202000c4</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-4d">
         <name>cx</name>
         <value>0x202000c6</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-4e">
         <name>Kp</name>
         <value>0x202000bc</value>
         <object_component_ref idref="oc-f4"/>
      </symbol>
      <symbol id="sm-4f">
         <name>SpeedL</name>
         <value>0x202000c0</value>
         <object_component_ref idref="oc-f5"/>
      </symbol>
      <symbol id="sm-50">
         <name>SpeedR</name>
         <value>0x202000c2</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-d7">
         <name>SYSCFG_DL_init</name>
         <value>0x1019</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-d8">
         <name>SYSCFG_DL_initPower</name>
         <value>0xc21</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-d9">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x80d</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-da">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xf79</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-db">
         <name>SYSCFG_DL_PWM_MOTOR_init</name>
         <value>0x8ed</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-dc">
         <name>SYSCFG_DL_UART_OPENMV_init</name>
         <value>0xd0d</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-dd">
         <name>gPWM_MOTORBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-e8">
         <name>Default_Handler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-e9">
         <name>Reset_Handler</name>
         <value>0x13a3</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-ea">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-eb">
         <name>NMI_Handler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ec">
         <name>HardFault_Handler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ed">
         <name>SVC_Handler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ee">
         <name>PendSV_Handler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ef">
         <name>SysTick_Handler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f0">
         <name>GROUP0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f1">
         <name>GROUP1_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f2">
         <name>TIMG8_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f3">
         <name>UART3_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f4">
         <name>ADC0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f5">
         <name>ADC1_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f6">
         <name>CANFD0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f7">
         <name>DAC0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f8">
         <name>SPI0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f9">
         <name>SPI1_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fa">
         <name>UART1_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fb">
         <name>UART2_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fc">
         <name>UART0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fd">
         <name>TIMG0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fe">
         <name>TIMG6_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ff">
         <name>TIMA0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-100">
         <name>TIMA1_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-101">
         <name>TIMG7_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-102">
         <name>TIMG12_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-103">
         <name>I2C0_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-104">
         <name>I2C1_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-105">
         <name>AES_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-106">
         <name>RTC_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-107">
         <name>DMA_IRQHandler</name>
         <value>0x139b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11b">
         <name>Motor_On</name>
         <value>0xff5</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-11c">
         <name>Set_Speed</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-8e"/>
      </symbol>
      <symbol id="sm-11d">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11e">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11f">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-120">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-121">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-122">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-123">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-124">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-125">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12e">
         <name>DL_Common_delayCycles</name>
         <value>0x1381</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-145">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1129</value>
         <object_component_ref idref="oc-11b"/>
      </symbol>
      <symbol id="sm-146">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x1371</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-147">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x110d</value>
         <object_component_ref idref="oc-11f"/>
      </symbol>
      <symbol id="sm-148">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1239</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-149">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x52d</value>
         <object_component_ref idref="oc-11c"/>
      </symbol>
      <symbol id="sm-159">
         <name>DL_UART_init</name>
         <value>0xcc5</value>
         <object_component_ref idref="oc-125"/>
      </symbol>
      <symbol id="sm-15a">
         <name>DL_UART_setClockConfig</name>
         <value>0x1329</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-15b">
         <name>DL_UART_transmitDataBlocking</name>
         <value>0x105d</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-166">
         <name>_c_int00_noargs</name>
         <value>0xfcd</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-167">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-173">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xe11</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-17b">
         <name>_system_pre_init</name>
         <value>0x13a7</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-186">
         <name>__TI_zero_init_nomemset</name>
         <value>0x12ad</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-18f">
         <name>__TI_decompress_none</name>
         <value>0x134d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-19a">
         <name>__TI_decompress_lzss</name>
         <value>0xb2d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>abort</name>
         <value>0x1395</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-1ae">
         <name>HOSTexit</name>
         <value>0x139f</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-1af">
         <name>C$$EXIT</name>
         <value>0x139e</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-1b5">
         <name>__aeabi_fadd</name>
         <value>0x9d3</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-1b6">
         <name>__addsf3</name>
         <value>0x9d3</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-1b7">
         <name>__aeabi_fsub</name>
         <value>0x9c9</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-1b8">
         <name>__subsf3</name>
         <value>0x9c9</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-1be">
         <name>__aeabi_dadd</name>
         <value>0x297</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-1bf">
         <name>__adddf3</name>
         <value>0x297</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-1c0">
         <name>__aeabi_dsub</name>
         <value>0x28d</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-1c1">
         <name>__subdf3</name>
         <value>0x28d</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>__aeabi_dmul</name>
         <value>0x729</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-1c8">
         <name>__muldf3</name>
         <value>0x729</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>__muldsi3</name>
         <value>0xe4d</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-1d4">
         <name>__aeabi_fmul</name>
         <value>0xaa1</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>__mulsf3</name>
         <value>0xaa1</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-1db">
         <name>__aeabi_ddiv</name>
         <value>0x421</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-1dc">
         <name>__divdf3</name>
         <value>0x421</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-1e2">
         <name>__aeabi_f2iz</name>
         <value>0xe89</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-1e3">
         <name>__fixsfsi</name>
         <value>0xe89</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-1e9">
         <name>__aeabi_d2uiz</name>
         <value>0xd55</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-1ea">
         <name>__fixunsdfsi</name>
         <value>0xd55</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>__aeabi_i2d</name>
         <value>0xf4d</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>__floatsidf</name>
         <value>0xf4d</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>__aeabi_i2f</name>
         <value>0xdd5</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>__floatsisf</name>
         <value>0xdd5</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>__aeabi_memcpy</name>
         <value>0x138d</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>__aeabi_memcpy4</name>
         <value>0x138d</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-200">
         <name>__aeabi_memcpy8</name>
         <value>0x138d</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-20a">
         <name>TI_memcpy_small</name>
         <value>0x133b</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-20b">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-20e">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-20f">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
