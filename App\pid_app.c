#include "pid_app.h"
#include "motor.h"
#include "stdbool.h"
void Gray_Task(void);

// /* PID 控制器实例 */
// PID_T pid_speed_left;  // 左轮速度环
// PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;        // 循迹环

extern float g_line_position_error;
// void Line_PID_control(void) // 循迹环控制
// {
//   int line_pid_output = 0;
  
//   // 使用位置式 PID 计算利用循迹环计算输出
//   line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
  
//   // 输出限幅
//   line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
  
//   // 将差值作用在速度环的目标量上
//   pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
//   pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
// }


PidParams_t pid_params_line = {
    .kp = 15.0f,        // 修正过大的Kp值
    .ki = 0.1f,
    .kd = 5.0f,         // 修正过大的Kd值
    .out_min = -100.0f,
    .out_max = 100.0f,
};
void PID_Init(void)
{
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  
//   pid_init(&pid_angle,
//            pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
//            0.0f, pid_params_angle.out_max);

  pid_set_target(&pid_line, 0);
}

bool pid_running = true; // PID 控制使能开关 - 启用循迹
int basic_speed = 25;// 基础速度 - 提高到合适值

void PID_Task(void)
{
    if(pid_running == false) return; // 检查PID使能状态

    int line_pid_output = 0;
    int motor_left_output = 0, motor_right_output = 0;

    // 单环循迹PID控制
    line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);

    // 输出限幅
    line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);

    // 直接计算左右电机输出（基础速度 ± 循迹修正量）
    motor_left_output = basic_speed - line_pid_output;   // 左转时减速
    motor_right_output = basic_speed + line_pid_output;  // 右转时加速

    // 电机输出限幅
    motor_left_output = pid_constrain(motor_left_output, 0, 100);    // 限制为正值
    motor_right_output = pid_constrain(motor_right_output, 0, 100);  // 限制为正值

    // 直接设置电机速度
    Set_Speed(0, motor_left_output);
    Set_Speed(1, motor_right_output);
}


void TIMER_1_INST_IRQHandler(void)
{
    // 清除定时器中断标志
    DL_Timer_clearInterruptStatus(TIMER_1_INST, DL_TIMER_INTERRUPT_ZERO_EVENT);

    Gray_Task();    // 读取灰度传感器数据
    PID_Task();     // 执行PID循迹控制
}

