#include "pid_app.h"
#include "motor.h"
#include "stdbool.h"
void Gray_Task(void);

// /* PID 控制器实例 */
// PID_T pid_speed_left;  // 左轮速度环
// PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;        // 循迹环

extern float g_line_position_error;
// void Line_PID_control(void) // 循迹环控制
// {
//   int line_pid_output = 0;
  
//   // 使用位置式 PID 计算利用循迹环计算输出
//   line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
  
//   // 输出限幅
//   line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
  
//   // 将差值作用在速度环的目标量上
//   pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
//   pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
// }


PidParams_t pid_params_line = {
    .kp = 10000000000.0f,        
    .ki = 0.1f,      
    .kd = 80.00f,      
    .out_min = -100.0f,
    .out_max = 100.0f,
};
void PID_Init(void)
{
  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  
//   pid_init(&pid_angle,
//            pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
//            0.0f, pid_params_angle.out_max);

  pid_set_target(&pid_line, 0);
}

bool pid_running = false; // PID 控制使能开关
int basic_speed = 15;// 基础速度

void PID_Task(void)
{
    // if(pid_running == false) return;
    
    int line_pid_output = 0;
    int motor_left_output = 0, motor_right_output = 0;
    
    // 单环循迹PID控制
    line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
    
    // 输出限幅
    line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
    
    // 直接计算左右电机输出（基础速度 ± 循迹修正量）
    motor_left_output = basic_speed - line_pid_output;   // 左转时减速
    motor_right_output = basic_speed + line_pid_output;  // 右转时加速
    
    // 电机输出限幅
    motor_left_output = pid_constrain(motor_left_output, -100, 100);
    motor_right_output = pid_constrain(motor_right_output, -100, 100);
    
    // 调试输出
    // Uart_Printf(&huart1,"{line_error}%.1f,{output}%d\r\n", g_line_position_error, line_pid_output);
    // Uart_Printf(&huart1,"{left}%d,{right}%d\r\n", motor_left_output, motor_right_output);
    
    // 直接设置电机速度
    Set_Speed(0, motor_left_output);
    Set_Speed(1, motor_right_output);
}


void TIMER_1_INST_IRQHandler(void)
{
    Gray_Task();
    PID_Task();

    // Set_Speed(0,20);
}

