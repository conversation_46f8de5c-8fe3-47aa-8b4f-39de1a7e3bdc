******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 18:58:45 2025

OUTPUT FILE NAME:   <my_project _02_copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001b39


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002220  00005de0  R  X
  SRAM                  20200000   00004000  000004f2  00003b0e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002220   00002220    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002098   00002098    r-x .text
  00002158    00002158    00000068   00000068    r-- .rodata
  000021c0    000021c0    00000060   00000060    r-- .cinit
20200000    20200000    000002f5   00000000    rw-
  20200000    20200000    000001b5   00000000    rw- .bss
  202001b8    202001b8    0000013d   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002098     
                  000000c0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000344    000001e8     motor.o (.text.Set_Speed)
                  0000052c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000006be    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000006c0    00000114     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000007d4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000008e0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000009e4    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00000acc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000bb0    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00000c8c    000000d8     grap_app.o (.text.Gray_Task)
                  00000d64    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000e3c    000000b4     pid_app.o (.text.PID_Task)
                  00000ef0    000000b0     IIC.o (.text.hardware_IIC_ReadByte)
                  00000fa0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000102c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000010b0    00000084     pid.o (.text.pid_formula_positional)
                  00001134    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000011b6    00000002     --HOLE-- [fill = 0]
                  000011b8    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001234    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000012b0    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_Gray_init)
                  00001314    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001376    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000013d4    00000050     IIC.o (.text.DL_I2C_startControllerTransfer)
                  00001424    00000050     pid.o (.text.pid_init)
                  00001474    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000014c0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001508    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_OPENMV_init)
                  00001550    00000048     pid.o (.text.pid_out_limit)
                  00001598    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000015da    00000002     --HOLE-- [fill = 0]
                  000015dc    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000161c    0000003e     pid.o (.text.pid_constrain)
                  0000165a    00000002     --HOLE-- [fill = 0]
                  0000165c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00001698    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000016d4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001710    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_1_init)
                  0000174c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001788    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000017c4    0000003c     empty.o (.text.main)
                  00001800    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000183c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001876    00000002     --HOLE-- [fill = 0]
                  00001878    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000018b2    00000002     --HOLE-- [fill = 0]
                  000018b4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000018ec    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00001924    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001958    00000034     pid_app.o (.text.PID_Init)
                  0000198c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000019c0    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000019ec    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00001a18    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00001a44    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001a6e    00000028     IIC.o (.text.DL_Common_updateReg)
                  00001a96    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001abe    00000002     --HOLE-- [fill = 0]
                  00001ac0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00001ae8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00001b10    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00001b38    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001b60    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00001b86    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00001bac    00000024     motor.o (.text.Motor_On)
                  00001bd0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00001bf2    00000022     pid.o (.text.pid_calculate_positional)
                  00001c14    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001c34    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001c52    00000002     --HOLE-- [fill = 0]
                  00001c54    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001c70    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001c8c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001ca8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00001cc4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001ce0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001cfc    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00001d18    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001d34    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001d50    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001d6c    0000001c     hardware_iic.o (.text.IIC_ReadByte)
                  00001d88    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001da0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001db8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001dd0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001de8    00000018     motor.o (.text.DL_GPIO_setPins)
                  00001e00    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001e18    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00001e30    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001e48    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001e60    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001e78    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001e90    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001ea8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001ec0    00000018     pid_app.o (.text.DL_Timer_clearInterruptStatus)
                  00001ed8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001ef0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001f08    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001f20    00000018     empty.o (.text.DL_Timer_startCounter)
                  00001f38    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001f50    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001f68    00000018     hardware_iic.o (.text.IIC_Get_Digtal)
                  00001f80    00000018     pid_app.o (.text.TIMA1_IRQHandler)
                  00001f98    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00001fb0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001fc6    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001fdc    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00001ff0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002004    00000014     IIC.o (.text.DL_I2C_getControllerStatus)
                  00002018    00000014     IIC.o (.text.DL_I2C_receiveControllerData)
                  0000202c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00002040    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002054    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002068    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000207c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000208e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000020a0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000020b2    00000002     --HOLE-- [fill = 0]
                  000020b4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000020c4    00000010     empty.o (.text.DL_SYSCTL_enableSleepOnExit)
                  000020d4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000020e4    00000010     pid.o (.text.pid_set_target)
                  000020f4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00002102    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00002110    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000211e    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002128    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00002132    00000002     --HOLE-- [fill = 0]
                  00002134    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000213c    00000006     libc.a : exit.c.obj (.text:abort)
                  00002142    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002146    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000214a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000214e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002152    00000006     --HOLE-- [fill = 0]

.cinit     0    000021c0    00000060     
                  000021c0    0000003a     (.cinit..data.load) [load image, compression = lzss]
                  000021fa    00000002     --HOLE-- [fill = 0]
                  000021fc    0000000c     (__TI_handler_table)
                  00002208    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002210    00000010     (__TI_cinit_table)

.rodata    0    00002158    00000068     
                  00002158    00000022     grap_app.o (.rodata.str1.6241306812405096412.1)
                  0000217a    00000002     ti_msp_dl_config.o (.rodata.gI2C_GrayClockConfig)
                  0000217c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_1TimerConfig)
                  00002190    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000021a1    00000001     --HOLE-- [fill = 0]
                  000021a2    0000000a     ti_msp_dl_config.o (.rodata.gUART_OPENMVConfig)
                  000021ac    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  000021b4    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  000021b7    00000003     ti_msp_dl_config.o (.rodata.gTIMER_1ClockConfig)
                  000021ba    00000002     ti_msp_dl_config.o (.rodata.gUART_OPENMVClockConfig)
                  000021bc    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b5     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_MOTORBackup)
                  202000bc    000000bc     (.common:gTIMER_1Backup)
                  20200178    0000003c     (.common:pid_line)
                  202001b4    00000001     (.common:Digtal)

.data      0    202001b8    0000013d     UNINITIALIZED
                  202001b8    00000100     grap_app.o (.data.Ch)
                  202002b8    00000020     grap_app.o (.data.gray_weights)
                  202002d8    00000014     pid_app.o (.data.pid_params_line)
                  202002ec    00000004     pid_app.o (.data.basic_speed)
                  202002f0    00000004     grap_app.o (.data.g_line_position_error)
                  202002f4    00000001     pid_app.o (.data.pid_running)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2292   48        376    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        188    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2488   240       376    
                                                              
    .\App\
       grap_app.o                     216    34        293    
       pid_app.o                      280    0         85     
    +--+------------------------------+------+---------+---------+
       Total:                         496    34        378    
                                                              
    .\Driver\
       motor.o                        568    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         568    0         0      
                                                              
    .\Gray\
       IIC.o                          336    0         0      
       hardware_iic.o                 52     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         388    0         0      
                                                              
    .\PID\
       pid.o                          396    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         396    0         0      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_i2c.o                       192    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         880    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658    17        0      
       copy_decompress_lzss.c.obj     124    0         0      
       sprintf.c.obj                  90     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       memccpy.c.obj                  34     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       memset16.S.obj                 14     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1088   17        0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       divdf3.S.obj                   268    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatsidf.S.obj                44     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memset.S.obj             14     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2012   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      94        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8320   385       1266   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002210 records: 2, size/record: 8, table size: 16
	.data: load addr=000021c0, load size=0000003a bytes, run addr=202001b8, run size=0000013d bytes, compression=lzss
	.bss: load addr=00002208, load size=00000008 bytes, run addr=20200000, run size=000001b5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000021fc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002143  ADC0_IRQHandler                 
00002143  ADC1_IRQHandler                 
00002143  AES_IRQHandler                  
00002146  C$$EXIT                         
00002143  CANFD0_IRQHandler               
202001b8  Ch                              
00002143  DAC0_IRQHandler                 
0000211f  DL_Common_delayCycles           
00001377  DL_I2C_fillControllerTXFIFO     
00001699  DL_I2C_flushControllerTXFIFO    
00001b87  DL_I2C_setClockConfig           
000008e1  DL_Timer_initFourCCPWMMode      
000009e5  DL_Timer_initTimerMode          
00001d19  DL_Timer_setCaptCompUpdateMethod
00001f09  DL_Timer_setCaptureCompareOutCtl
000020d5  DL_Timer_setCaptureCompareValue 
00001d35  DL_Timer_setClockConfig         
000014c1  DL_UART_init                    
0000207d  DL_UART_setClockConfig          
00002143  DMA_IRQHandler                  
00002143  Default_Handler                 
202001b4  Digtal                          
00002143  GROUP0_IRQHandler               
00002143  GROUP1_IRQHandler               
00000c8d  Gray_Task                       
00002147  HOSTexit                        
00002143  HardFault_Handler               
00002143  I2C0_IRQHandler                 
00002143  I2C1_IRQHandler                 
00001f69  IIC_Get_Digtal                  
00001d6d  IIC_ReadByte                    
00001bad  Motor_On                        
00002143  NMI_Handler                     
00001959  PID_Init                        
00000e3d  PID_Task                        
00002143  PendSV_Handler                  
00002143  RTC_IRQHandler                  
0000214b  Reset_Handler                   
00002143  SPI0_IRQHandler                 
00002143  SPI1_IRQHandler                 
00002143  SVC_Handler                     
000006c1  SYSCFG_DL_GPIO_init             
000012b1  SYSCFG_DL_I2C_Gray_init         
00000bb1  SYSCFG_DL_PWM_MOTOR_init        
00001a45  SYSCFG_DL_SYSCTL_init           
00001711  SYSCFG_DL_TIMER_1_init          
00001509  SYSCFG_DL_UART_OPENMV_init      
0000198d  SYSCFG_DL_init                  
000011b9  SYSCFG_DL_initPower             
00000345  Set_Speed                       
00002143  SysTick_Handler                 
00002143  TIMA0_IRQHandler                
00001f81  TIMA1_IRQHandler                
00002143  TIMG0_IRQHandler                
00002143  TIMG12_IRQHandler               
00002143  TIMG6_IRQHandler                
00002143  TIMG7_IRQHandler                
00002143  TIMG8_IRQHandler                
0000208f  TI_memcpy_small                 
00002111  TI_memset_small                 
00002143  UART0_IRQHandler                
00002143  UART1_IRQHandler                
00002143  UART2_IRQHandler                
00002143  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002210  __TI_CINIT_Base                 
00002220  __TI_CINIT_Limit                
00002220  __TI_CINIT_Warm                 
000021fc  __TI_Handler_Table_Base         
00002208  __TI_Handler_Table_Limit        
00001801  __TI_auto_init_nobinit_nopinit  
00001235  __TI_decompress_lzss            
000020a1  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
000000c1  __TI_printfi_minimal            
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00001fc7  __TI_zero_init_nomemset         
00000537  __adddf3                        
00000d6f  __addsf3                        
00001599  __aeabi_d2uiz                   
00000537  __aeabi_dadd                    
000007d5  __aeabi_ddiv                    
00000acd  __aeabi_dmul                    
0000052d  __aeabi_dsub                    
000018b5  __aeabi_f2iz                    
00000d6f  __aeabi_fadd                    
00001315  __aeabi_fcmpeq                  
00001351  __aeabi_fcmpge                  
00001365  __aeabi_fcmpgt                  
0000133d  __aeabi_fcmple                  
00001329  __aeabi_fcmplt                  
00001135  __aeabi_fdiv                    
00000fa1  __aeabi_fmul                    
00000d65  __aeabi_fsub                    
00001a19  __aeabi_i2d                     
0000174d  __aeabi_i2f                     
000006bf  __aeabi_idiv0                   
00002135  __aeabi_memcpy                  
00002135  __aeabi_memcpy4                 
00002135  __aeabi_memcpy8                 
000020f5  __aeabi_memset                  
000020f5  __aeabi_memset4                 
000020f5  __aeabi_memset8                 
00001b11  __aeabi_ui2f                    
000015dd  __aeabi_uidiv                   
000015dd  __aeabi_uidivmod                
ffffffff  __binit__                       
0000183d  __cmpsf2                        
000007d5  __divdf3                        
00001135  __divsf3                        
0000183d  __eqsf2                         
000018b5  __fixsfsi                       
00001599  __fixunsdfsi                    
00001a19  __floatsidf                     
0000174d  __floatsisf                     
00001b11  __floatunsisf                   
00001789  __gesf2                         
00001789  __gtsf2                         
0000183d  __lesf2                         
0000183d  __ltsf2                         
UNDEFED   __mpu_init                      
00000acd  __muldf3                        
00001879  __muldsi3                       
00000fa1  __mulsf3                        
0000183d  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
0000052d  __subdf3                        
00000d65  __subsf3                        
00001b39  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
0000214f  _system_pre_init                
0000213d  abort                           
202002ec  basic_speed                     
ffffffff  binit                           
20200000  gPWM_MOTORBackup                
202000bc  gTIMER_1Backup                  
202002f0  g_line_position_error           
202002b8  gray_weights                    
00000ef1  hardware_IIC_ReadByte           
00000000  interruptVectors                
000017c5  main                            
00001bd1  memccpy                         
00001bf3  pid_calculate_positional        
0000161d  pid_constrain                   
00001425  pid_init                        
20200178  pid_line                        
202002d8  pid_params_line                 
202002f4  pid_running                     
000020e5  pid_set_target                  
000018ed  sprintf                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  __TI_printfi_minimal            
00000200  __STACK_SIZE                    
00000345  Set_Speed                       
0000052d  __aeabi_dsub                    
0000052d  __subdf3                        
00000537  __adddf3                        
00000537  __aeabi_dadd                    
000006bf  __aeabi_idiv0                   
000006c1  SYSCFG_DL_GPIO_init             
000007d5  __aeabi_ddiv                    
000007d5  __divdf3                        
000008e1  DL_Timer_initFourCCPWMMode      
000009e5  DL_Timer_initTimerMode          
00000acd  __aeabi_dmul                    
00000acd  __muldf3                        
00000bb1  SYSCFG_DL_PWM_MOTOR_init        
00000c8d  Gray_Task                       
00000d65  __aeabi_fsub                    
00000d65  __subsf3                        
00000d6f  __addsf3                        
00000d6f  __aeabi_fadd                    
00000e3d  PID_Task                        
00000ef1  hardware_IIC_ReadByte           
00000fa1  __aeabi_fmul                    
00000fa1  __mulsf3                        
00001135  __aeabi_fdiv                    
00001135  __divsf3                        
000011b9  SYSCFG_DL_initPower             
00001235  __TI_decompress_lzss            
000012b1  SYSCFG_DL_I2C_Gray_init         
00001315  __aeabi_fcmpeq                  
00001329  __aeabi_fcmplt                  
0000133d  __aeabi_fcmple                  
00001351  __aeabi_fcmpge                  
00001365  __aeabi_fcmpgt                  
00001377  DL_I2C_fillControllerTXFIFO     
00001425  pid_init                        
000014c1  DL_UART_init                    
00001509  SYSCFG_DL_UART_OPENMV_init      
00001599  __aeabi_d2uiz                   
00001599  __fixunsdfsi                    
000015dd  __aeabi_uidiv                   
000015dd  __aeabi_uidivmod                
0000161d  pid_constrain                   
00001699  DL_I2C_flushControllerTXFIFO    
00001711  SYSCFG_DL_TIMER_1_init          
0000174d  __aeabi_i2f                     
0000174d  __floatsisf                     
00001789  __gesf2                         
00001789  __gtsf2                         
000017c5  main                            
00001801  __TI_auto_init_nobinit_nopinit  
0000183d  __cmpsf2                        
0000183d  __eqsf2                         
0000183d  __lesf2                         
0000183d  __ltsf2                         
0000183d  __nesf2                         
00001879  __muldsi3                       
000018b5  __aeabi_f2iz                    
000018b5  __fixsfsi                       
000018ed  sprintf                         
00001959  PID_Init                        
0000198d  SYSCFG_DL_init                  
00001a19  __aeabi_i2d                     
00001a19  __floatsidf                     
00001a45  SYSCFG_DL_SYSCTL_init           
00001b11  __aeabi_ui2f                    
00001b11  __floatunsisf                   
00001b39  _c_int00_noargs                 
00001b87  DL_I2C_setClockConfig           
00001bad  Motor_On                        
00001bd1  memccpy                         
00001bf3  pid_calculate_positional        
00001d19  DL_Timer_setCaptCompUpdateMethod
00001d35  DL_Timer_setClockConfig         
00001d6d  IIC_ReadByte                    
00001f09  DL_Timer_setCaptureCompareOutCtl
00001f69  IIC_Get_Digtal                  
00001f81  TIMA1_IRQHandler                
00001fc7  __TI_zero_init_nomemset         
0000207d  DL_UART_setClockConfig          
0000208f  TI_memcpy_small                 
000020a1  __TI_decompress_none            
000020d5  DL_Timer_setCaptureCompareValue 
000020e5  pid_set_target                  
000020f5  __aeabi_memset                  
000020f5  __aeabi_memset4                 
000020f5  __aeabi_memset8                 
00002111  TI_memset_small                 
0000211f  DL_Common_delayCycles           
00002135  __aeabi_memcpy                  
00002135  __aeabi_memcpy4                 
00002135  __aeabi_memcpy8                 
0000213d  abort                           
00002143  ADC0_IRQHandler                 
00002143  ADC1_IRQHandler                 
00002143  AES_IRQHandler                  
00002143  CANFD0_IRQHandler               
00002143  DAC0_IRQHandler                 
00002143  DMA_IRQHandler                  
00002143  Default_Handler                 
00002143  GROUP0_IRQHandler               
00002143  GROUP1_IRQHandler               
00002143  HardFault_Handler               
00002143  I2C0_IRQHandler                 
00002143  I2C1_IRQHandler                 
00002143  NMI_Handler                     
00002143  PendSV_Handler                  
00002143  RTC_IRQHandler                  
00002143  SPI0_IRQHandler                 
00002143  SPI1_IRQHandler                 
00002143  SVC_Handler                     
00002143  SysTick_Handler                 
00002143  TIMA0_IRQHandler                
00002143  TIMG0_IRQHandler                
00002143  TIMG12_IRQHandler               
00002143  TIMG6_IRQHandler                
00002143  TIMG7_IRQHandler                
00002143  TIMG8_IRQHandler                
00002143  UART0_IRQHandler                
00002143  UART1_IRQHandler                
00002143  UART2_IRQHandler                
00002143  UART3_IRQHandler                
00002146  C$$EXIT                         
00002147  HOSTexit                        
0000214b  Reset_Handler                   
0000214f  _system_pre_init                
000021fc  __TI_Handler_Table_Base         
00002208  __TI_Handler_Table_Limit        
00002210  __TI_CINIT_Base                 
00002220  __TI_CINIT_Limit                
00002220  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_MOTORBackup                
202000bc  gTIMER_1Backup                  
20200178  pid_line                        
202001b4  Digtal                          
202001b8  Ch                              
202002b8  gray_weights                    
202002d8  pid_params_line                 
202002ec  basic_speed                     
202002f0  g_line_position_error           
202002f4  pid_running                     
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[166 symbols]
