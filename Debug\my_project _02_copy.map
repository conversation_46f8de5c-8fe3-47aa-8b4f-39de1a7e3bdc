******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 18:13:08 2025

OUTPUT FILE NAME:   <my_project _02_copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001b25


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  000021c0  00005e40  R  X
  SRAM                  20200000   00004000  000004f1  00003b0f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000021c0   000021c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002038   00002038    r-x .text
  000020f8    000020f8    00000068   00000068    r-- .rodata
  00002160    00002160    00000060   00000060    r-- .cinit
20200000    20200000    000002f4   00000000    rw-
  20200000    20200000    000001b9   00000000    rw- .bss
  202001bc    202001bc    00000138   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002038     
                  000000c0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000344    000001e8     motor.o (.text.Set_Speed)
                  0000052c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000006be    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000006c0    00000114     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000007d4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000008e0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000009e4    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00000acc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000bb0    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_MOTOR_init)
                  00000c8c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000d64    000000d4     grap_app.o (.text.Gray_Task)
                  00000e38    000000b0     IIC.o (.text.hardware_IIC_ReadByte)
                  00000ee8    000000a8     pid_app.o (.text.PID_Task)
                  00000f90    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000101c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000010a0    00000084     pid.o (.text.pid_formula_positional)
                  00001124    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000011a6    00000002     --HOLE-- [fill = 0]
                  000011a8    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001224    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000012a0    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_Gray_init)
                  00001304    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001366    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000013c4    00000050     IIC.o (.text.DL_I2C_startControllerTransfer)
                  00001414    00000050     pid.o (.text.pid_init)
                  00001464    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000014b0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000014f8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_OPENMV_init)
                  00001540    00000048     pid.o (.text.pid_out_limit)
                  00001588    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000015ca    00000002     --HOLE-- [fill = 0]
                  000015cc    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000160c    0000003e     pid.o (.text.pid_constrain)
                  0000164a    00000002     --HOLE-- [fill = 0]
                  0000164c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00001688    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000016c4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001700    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_1_init)
                  0000173c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001778    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000017b4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000017f0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000182a    00000002     --HOLE-- [fill = 0]
                  0000182c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00001866    00000002     --HOLE-- [fill = 0]
                  00001868    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000018a0    00000038     empty.o (.text.main)
                  000018d8    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00001910    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001944    00000034     pid_app.o (.text.PID_Init)
                  00001978    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000019ac    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000019d8    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00001a04    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00001a30    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001a5a    00000028     IIC.o (.text.DL_Common_updateReg)
                  00001a82    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001aaa    00000002     --HOLE-- [fill = 0]
                  00001aac    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00001ad4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00001afc    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00001b24    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001b4c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00001b72    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00001b98    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00001bba    00000022     pid.o (.text.pid_calculate_positional)
                  00001bdc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001bfc    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001c1a    00000002     --HOLE-- [fill = 0]
                  00001c1c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001c38    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001c54    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001c70    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00001c8c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001ca8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001cc4    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00001ce0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001cfc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001d18    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001d34    0000001c     hardware_iic.o (.text.IIC_ReadByte)
                  00001d50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001d68    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001d80    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001d98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001db0    00000018     motor.o (.text.DL_GPIO_setPins)
                  00001dc8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001de0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00001df8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001e10    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001e28    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001e40    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001e58    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001e70    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001e88    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001ea0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001eb8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001ed0    00000018     empty.o (.text.DL_Timer_startCounter)
                  00001ee8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001f00    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001f18    00000018     hardware_iic.o (.text.IIC_Get_Digtal)
                  00001f30    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00001f48    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001f5e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001f74    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00001f88    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001f9c    00000014     IIC.o (.text.DL_I2C_getControllerStatus)
                  00001fb0    00000014     IIC.o (.text.DL_I2C_receiveControllerData)
                  00001fc4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00001fd8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001fec    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002000    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002014    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002026    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002038    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000204a    00000002     --HOLE-- [fill = 0]
                  0000204c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000205c    00000010     empty.o (.text.DL_SYSCTL_enableSleepOnExit)
                  0000206c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000207c    00000010     pid.o (.text.pid_set_target)
                  0000208c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000209a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000020a8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000020b6    0000000c     pid_app.o (.text.TIMA1_IRQHandler)
                  000020c2    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000020cc    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  000020d6    00000002     --HOLE-- [fill = 0]
                  000020d8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000020e0    00000006     libc.a : exit.c.obj (.text:abort)
                  000020e6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000020ea    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000020ee    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000020f2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000020f6    00000002     --HOLE-- [fill = 0]

.cinit     0    00002160    00000060     
                  00002160    0000003c     (.cinit..data.load) [load image, compression = lzss]
                  0000219c    0000000c     (__TI_handler_table)
                  000021a8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000021b0    00000010     (__TI_cinit_table)

.rodata    0    000020f8    00000068     
                  000020f8    00000022     grap_app.o (.rodata.str1.6241306812405096412.1)
                  0000211a    00000002     ti_msp_dl_config.o (.rodata.gI2C_GrayClockConfig)
                  0000211c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_1TimerConfig)
                  00002130    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00002141    00000001     --HOLE-- [fill = 0]
                  00002142    0000000a     ti_msp_dl_config.o (.rodata.gUART_OPENMVConfig)
                  0000214c    00000008     ti_msp_dl_config.o (.rodata.gPWM_MOTORConfig)
                  00002154    00000003     ti_msp_dl_config.o (.rodata.gPWM_MOTORClockConfig)
                  00002157    00000003     ti_msp_dl_config.o (.rodata.gTIMER_1ClockConfig)
                  0000215a    00000002     ti_msp_dl_config.o (.rodata.gUART_OPENMVClockConfig)
                  0000215c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b9     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_MOTORBackup)
                  202000bc    000000bc     (.common:gTIMER_1Backup)
                  20200178    0000003c     (.common:pid_line)
                  202001b4    00000004     (.common:g_line_position_error)
                  202001b8    00000001     (.common:Digtal)

.data      0    202001bc    00000138     UNINITIALIZED
                  202001bc    00000100     grap_app.o (.data.Ch)
                  202002bc    00000020     grap_app.o (.data.gray_weights)
                  202002dc    00000014     pid_app.o (.data.pid_params_line)
                  202002f0    00000004     pid_app.o (.data.basic_speed)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2292   48        376    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        184    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2484   240       376    
                                                              
    .\App\
       grap_app.o                     212    34        293    
       pid_app.o                      232    0         84     
    +--+------------------------------+------+---------+---------+
       Total:                         444    34        377    
                                                              
    .\Driver\
       motor.o                        532    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         532    0         0      
                                                              
    .\Gray\
       IIC.o                          336    0         0      
       hardware_iic.o                 52     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         388    0         0      
                                                              
    .\PID\
       pid.o                          396    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         396    0         0      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_i2c.o                       192    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         880    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658    17        0      
       copy_decompress_lzss.c.obj     124    0         0      
       sprintf.c.obj                  90     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       memccpy.c.obj                  34     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       memset16.S.obj                 14     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1088   17        0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       divdf3.S.obj                   268    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatsidf.S.obj                44     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memset.S.obj             14     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2012   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      96        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8228   387       1265   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000021b0 records: 2, size/record: 8, table size: 16
	.data: load addr=00002160, load size=0000003c bytes, run addr=202001bc, run size=00000138 bytes, compression=lzss
	.bss: load addr=000021a8, load size=00000008 bytes, run addr=20200000, run size=000001b9 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000219c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
000020e7  ADC0_IRQHandler                 
000020e7  ADC1_IRQHandler                 
000020e7  AES_IRQHandler                  
000020ea  C$$EXIT                         
000020e7  CANFD0_IRQHandler               
202001bc  Ch                              
000020e7  DAC0_IRQHandler                 
000020c3  DL_Common_delayCycles           
00001367  DL_I2C_fillControllerTXFIFO     
00001689  DL_I2C_flushControllerTXFIFO    
00001b73  DL_I2C_setClockConfig           
000008e1  DL_Timer_initFourCCPWMMode      
000009e5  DL_Timer_initTimerMode          
00001ce1  DL_Timer_setCaptCompUpdateMethod
00001eb9  DL_Timer_setCaptureCompareOutCtl
0000206d  DL_Timer_setCaptureCompareValue 
00001cfd  DL_Timer_setClockConfig         
000014b1  DL_UART_init                    
00002015  DL_UART_setClockConfig          
000020e7  DMA_IRQHandler                  
000020e7  Default_Handler                 
202001b8  Digtal                          
000020e7  GROUP0_IRQHandler               
000020e7  GROUP1_IRQHandler               
00000d65  Gray_Task                       
000020eb  HOSTexit                        
000020e7  HardFault_Handler               
000020e7  I2C0_IRQHandler                 
000020e7  I2C1_IRQHandler                 
00001f19  IIC_Get_Digtal                  
00001d35  IIC_ReadByte                    
000020e7  NMI_Handler                     
00001945  PID_Init                        
00000ee9  PID_Task                        
000020e7  PendSV_Handler                  
000020e7  RTC_IRQHandler                  
000020ef  Reset_Handler                   
000020e7  SPI0_IRQHandler                 
000020e7  SPI1_IRQHandler                 
000020e7  SVC_Handler                     
000006c1  SYSCFG_DL_GPIO_init             
000012a1  SYSCFG_DL_I2C_Gray_init         
00000bb1  SYSCFG_DL_PWM_MOTOR_init        
00001a31  SYSCFG_DL_SYSCTL_init           
00001701  SYSCFG_DL_TIMER_1_init          
000014f9  SYSCFG_DL_UART_OPENMV_init      
00001979  SYSCFG_DL_init                  
000011a9  SYSCFG_DL_initPower             
00000345  Set_Speed                       
000020e7  SysTick_Handler                 
000020e7  TIMA0_IRQHandler                
000020b7  TIMA1_IRQHandler                
000020e7  TIMG0_IRQHandler                
000020e7  TIMG12_IRQHandler               
000020e7  TIMG6_IRQHandler                
000020e7  TIMG7_IRQHandler                
000020e7  TIMG8_IRQHandler                
00002027  TI_memcpy_small                 
000020a9  TI_memset_small                 
000020e7  UART0_IRQHandler                
000020e7  UART1_IRQHandler                
000020e7  UART2_IRQHandler                
000020e7  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000021b0  __TI_CINIT_Base                 
000021c0  __TI_CINIT_Limit                
000021c0  __TI_CINIT_Warm                 
0000219c  __TI_Handler_Table_Base         
000021a8  __TI_Handler_Table_Limit        
000017b5  __TI_auto_init_nobinit_nopinit  
00001225  __TI_decompress_lzss            
00002039  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
000000c1  __TI_printfi_minimal            
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00001f5f  __TI_zero_init_nomemset         
00000537  __adddf3                        
00000c97  __addsf3                        
00001589  __aeabi_d2uiz                   
00000537  __aeabi_dadd                    
000007d5  __aeabi_ddiv                    
00000acd  __aeabi_dmul                    
0000052d  __aeabi_dsub                    
00001869  __aeabi_f2iz                    
00000c97  __aeabi_fadd                    
00001305  __aeabi_fcmpeq                  
00001341  __aeabi_fcmpge                  
00001355  __aeabi_fcmpgt                  
0000132d  __aeabi_fcmple                  
00001319  __aeabi_fcmplt                  
00001125  __aeabi_fdiv                    
00000f91  __aeabi_fmul                    
00000c8d  __aeabi_fsub                    
00001a05  __aeabi_i2d                     
0000173d  __aeabi_i2f                     
000006bf  __aeabi_idiv0                   
000020d9  __aeabi_memcpy                  
000020d9  __aeabi_memcpy4                 
000020d9  __aeabi_memcpy8                 
0000208d  __aeabi_memset                  
0000208d  __aeabi_memset4                 
0000208d  __aeabi_memset8                 
00001afd  __aeabi_ui2f                    
000015cd  __aeabi_uidiv                   
000015cd  __aeabi_uidivmod                
ffffffff  __binit__                       
000017f1  __cmpsf2                        
000007d5  __divdf3                        
00001125  __divsf3                        
000017f1  __eqsf2                         
00001869  __fixsfsi                       
00001589  __fixunsdfsi                    
00001a05  __floatsidf                     
0000173d  __floatsisf                     
00001afd  __floatunsisf                   
00001779  __gesf2                         
00001779  __gtsf2                         
000017f1  __lesf2                         
000017f1  __ltsf2                         
UNDEFED   __mpu_init                      
00000acd  __muldf3                        
0000182d  __muldsi3                       
00000f91  __mulsf3                        
000017f1  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
0000052d  __subdf3                        
00000c8d  __subsf3                        
00001b25  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000020f3  _system_pre_init                
000020e1  abort                           
202002f0  basic_speed                     
ffffffff  binit                           
20200000  gPWM_MOTORBackup                
202000bc  gTIMER_1Backup                  
202001b4  g_line_position_error           
202002bc  gray_weights                    
00000e39  hardware_IIC_ReadByte           
00000000  interruptVectors                
000018a1  main                            
00001b99  memccpy                         
00001bbb  pid_calculate_positional        
0000160d  pid_constrain                   
00001415  pid_init                        
20200178  pid_line                        
202002dc  pid_params_line                 
0000207d  pid_set_target                  
000018d9  sprintf                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  __TI_printfi_minimal            
00000200  __STACK_SIZE                    
00000345  Set_Speed                       
0000052d  __aeabi_dsub                    
0000052d  __subdf3                        
00000537  __adddf3                        
00000537  __aeabi_dadd                    
000006bf  __aeabi_idiv0                   
000006c1  SYSCFG_DL_GPIO_init             
000007d5  __aeabi_ddiv                    
000007d5  __divdf3                        
000008e1  DL_Timer_initFourCCPWMMode      
000009e5  DL_Timer_initTimerMode          
00000acd  __aeabi_dmul                    
00000acd  __muldf3                        
00000bb1  SYSCFG_DL_PWM_MOTOR_init        
00000c8d  __aeabi_fsub                    
00000c8d  __subsf3                        
00000c97  __addsf3                        
00000c97  __aeabi_fadd                    
00000d65  Gray_Task                       
00000e39  hardware_IIC_ReadByte           
00000ee9  PID_Task                        
00000f91  __aeabi_fmul                    
00000f91  __mulsf3                        
00001125  __aeabi_fdiv                    
00001125  __divsf3                        
000011a9  SYSCFG_DL_initPower             
00001225  __TI_decompress_lzss            
000012a1  SYSCFG_DL_I2C_Gray_init         
00001305  __aeabi_fcmpeq                  
00001319  __aeabi_fcmplt                  
0000132d  __aeabi_fcmple                  
00001341  __aeabi_fcmpge                  
00001355  __aeabi_fcmpgt                  
00001367  DL_I2C_fillControllerTXFIFO     
00001415  pid_init                        
000014b1  DL_UART_init                    
000014f9  SYSCFG_DL_UART_OPENMV_init      
00001589  __aeabi_d2uiz                   
00001589  __fixunsdfsi                    
000015cd  __aeabi_uidiv                   
000015cd  __aeabi_uidivmod                
0000160d  pid_constrain                   
00001689  DL_I2C_flushControllerTXFIFO    
00001701  SYSCFG_DL_TIMER_1_init          
0000173d  __aeabi_i2f                     
0000173d  __floatsisf                     
00001779  __gesf2                         
00001779  __gtsf2                         
000017b5  __TI_auto_init_nobinit_nopinit  
000017f1  __cmpsf2                        
000017f1  __eqsf2                         
000017f1  __lesf2                         
000017f1  __ltsf2                         
000017f1  __nesf2                         
0000182d  __muldsi3                       
00001869  __aeabi_f2iz                    
00001869  __fixsfsi                       
000018a1  main                            
000018d9  sprintf                         
00001945  PID_Init                        
00001979  SYSCFG_DL_init                  
00001a05  __aeabi_i2d                     
00001a05  __floatsidf                     
00001a31  SYSCFG_DL_SYSCTL_init           
00001afd  __aeabi_ui2f                    
00001afd  __floatunsisf                   
00001b25  _c_int00_noargs                 
00001b73  DL_I2C_setClockConfig           
00001b99  memccpy                         
00001bbb  pid_calculate_positional        
00001ce1  DL_Timer_setCaptCompUpdateMethod
00001cfd  DL_Timer_setClockConfig         
00001d35  IIC_ReadByte                    
00001eb9  DL_Timer_setCaptureCompareOutCtl
00001f19  IIC_Get_Digtal                  
00001f5f  __TI_zero_init_nomemset         
00002015  DL_UART_setClockConfig          
00002027  TI_memcpy_small                 
00002039  __TI_decompress_none            
0000206d  DL_Timer_setCaptureCompareValue 
0000207d  pid_set_target                  
0000208d  __aeabi_memset                  
0000208d  __aeabi_memset4                 
0000208d  __aeabi_memset8                 
000020a9  TI_memset_small                 
000020b7  TIMA1_IRQHandler                
000020c3  DL_Common_delayCycles           
000020d9  __aeabi_memcpy                  
000020d9  __aeabi_memcpy4                 
000020d9  __aeabi_memcpy8                 
000020e1  abort                           
000020e7  ADC0_IRQHandler                 
000020e7  ADC1_IRQHandler                 
000020e7  AES_IRQHandler                  
000020e7  CANFD0_IRQHandler               
000020e7  DAC0_IRQHandler                 
000020e7  DMA_IRQHandler                  
000020e7  Default_Handler                 
000020e7  GROUP0_IRQHandler               
000020e7  GROUP1_IRQHandler               
000020e7  HardFault_Handler               
000020e7  I2C0_IRQHandler                 
000020e7  I2C1_IRQHandler                 
000020e7  NMI_Handler                     
000020e7  PendSV_Handler                  
000020e7  RTC_IRQHandler                  
000020e7  SPI0_IRQHandler                 
000020e7  SPI1_IRQHandler                 
000020e7  SVC_Handler                     
000020e7  SysTick_Handler                 
000020e7  TIMA0_IRQHandler                
000020e7  TIMG0_IRQHandler                
000020e7  TIMG12_IRQHandler               
000020e7  TIMG6_IRQHandler                
000020e7  TIMG7_IRQHandler                
000020e7  TIMG8_IRQHandler                
000020e7  UART0_IRQHandler                
000020e7  UART1_IRQHandler                
000020e7  UART2_IRQHandler                
000020e7  UART3_IRQHandler                
000020ea  C$$EXIT                         
000020eb  HOSTexit                        
000020ef  Reset_Handler                   
000020f3  _system_pre_init                
0000219c  __TI_Handler_Table_Base         
000021a8  __TI_Handler_Table_Limit        
000021b0  __TI_CINIT_Base                 
000021c0  __TI_CINIT_Limit                
000021c0  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_MOTORBackup                
202000bc  gTIMER_1Backup                  
20200178  pid_line                        
202001b4  g_line_position_error           
202001b8  Digtal                          
202001bc  Ch                              
202002bc  gray_weights                    
202002dc  pid_params_line                 
202002f0  basic_speed                     
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[164 symbols]
