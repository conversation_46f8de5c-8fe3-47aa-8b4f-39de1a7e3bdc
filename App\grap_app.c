#include "ti_msp_dl_config.h"
#include "hardware_iic.h"
#include "gw_grayscale_sensor.h"

unsigned char Digtal; // ������
char Ch[256] = {0};

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; // 8 ·�Ҷ�ͨ��Ȩ�ر�

float g_line_position_error = 0.0f; // 循迹误差值，初始化为0

void Gray_Init(void)
{

}

void Gray_Task(void)
{
    //��ȡ���������������
     Digtal = ~IIC_Get_Digtal();  // 或者用 MCU_GetDigital()

    // 打印8路数字量（保留）
    sprintf(Ch,"Digital %d-%d-%d-%d-%d-%d-%d-%d\r\n",
        (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
        (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01);

    // uart1_send_string(Ch);
    // uart1_send_char('\n');
  
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0)
    {
      g_line_position_error = weighted_sum / (float)black_line_count;
    }
    else
    {
      // 没有检测到黑线时，保持上一次的误差值
      // g_line_position_error 保持不变
    }
}
