<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v3.2.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x6693c31f</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1191</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Driver\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-26">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-93">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-94">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-95">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-96">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-97">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-98">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-99">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-9a">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixunsdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-9b">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-9c">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-9d">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-9e">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-9f">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-a0">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-a1">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-a2">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.Set_Speed</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x288</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.adddf3_subdf3</name>
         <load_address>0x348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x348</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.__divdf3</name>
         <load_address>0x4dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4dc</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-98"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.Motor_Ctrl</name>
         <load_address>0x5e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5e8</run_address>
         <size>0xf8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x6e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e0</run_address>
         <size>0xf8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.__muldf3</name>
         <load_address>0x7d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d8</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text</name>
         <load_address>0x8bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8bc</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.SYSCFG_DL_PWM_MOTOR_init</name>
         <load_address>0x994</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x994</run_address>
         <size>0xd0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_Timer_initPWMMode</name>
         <load_address>0xa64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa64</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.__mulsf3</name>
         <load_address>0xb28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb28</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.UART1_IRQHandler</name>
         <load_address>0xbb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbb4</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.DL_TimerA_initPWMMode</name>
         <load_address>0xc3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc3c</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.text.Motor_Off</name>
         <load_address>0xcbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcbc</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xd34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd34</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-74">
         <name>.text.main</name>
         <load_address>0xdac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdac</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xe20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe20</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0xe78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe78</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_UART_init</name>
         <load_address>0xec4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec4</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.SYSCFG_DL_UART_OPENMV_init</name>
         <load_address>0xf0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf0c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.__fixunsdfsi</name>
         <load_address>0xf54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf54</run_address>
         <size>0x42</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9a"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.__floatsisf</name>
         <load_address>0xf98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf98</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9c"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xfd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfd4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.__muldsi3</name>
         <load_address>0x1010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1010</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.__fixsfsi</name>
         <load_address>0x104c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x104c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-99"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1084</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1084</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-91">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x10b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10b8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x10e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10e4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.__floatsidf</name>
         <load_address>0x1110</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1110</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9b"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x113c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x113c</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-145">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1166</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1166</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1190</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1190</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.Motor_On</name>
         <load_address>0x11b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11b8</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x11dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11dc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1200</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1200</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.DL_UART_transmitDataBlocking</name>
         <load_address>0x1220</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1220</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1240</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1240</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1260</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1260</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x127c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x127c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1298</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1298</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x12b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12b4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x12d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12d0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x12ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12ec</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x1308</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1308</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1324</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1324</run_address>
         <size>0x1c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1340</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1340</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1358</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1358</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1370</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1370</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1388</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1388</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x13a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x13b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13b8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x13d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13d0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x13e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13e8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1400</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1400</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1418</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1418</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1430</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1430</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1448</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1448</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1460</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1460</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1476</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1476</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x148c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x148c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-62">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x14a2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14a2</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x14b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14b6</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x14ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14ca</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x14e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14e0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x14f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14f4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1508</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1508</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x151c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x151c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x1530</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1530</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x1542</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1542</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x1554</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1554</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a1"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x1566</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1566</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-119">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x1578</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1578</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x1588</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1588</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1598</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x15a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15a4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9d"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x15ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15ac</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-70">
         <name>.text._system_pre_init</name>
         <load_address>0x15b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15b0</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text:abort</name>
         <load_address>0x15b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15b4</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-189">
         <name>.cinit..data.load</name>
         <load_address>0x15d0</load_address>
         <readonly>true</readonly>
         <run_address>0x15d0</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-187">
         <name>__TI_handler_table</name>
         <load_address>0x15e4</load_address>
         <readonly>true</readonly>
         <run_address>0x15e4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18a">
         <name>.cinit..bss.load</name>
         <load_address>0x15f0</load_address>
         <readonly>true</readonly>
         <run_address>0x15f0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-188">
         <name>__TI_cinit_table</name>
         <load_address>0x15f8</load_address>
         <readonly>true</readonly>
         <run_address>0x15f8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-12c">
         <name>.rodata.gUART_OPENMVConfig</name>
         <load_address>0x15b8</load_address>
         <readonly>true</readonly>
         <run_address>0x15b8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.rodata.gUART_OPENMVClockConfig</name>
         <load_address>0x15c2</load_address>
         <readonly>true</readonly>
         <run_address>0x15c2</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.rodata.gPWM_MOTORConfig</name>
         <load_address>0x15c4</load_address>
         <readonly>true</readonly>
         <run_address>0x15c4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-123">
         <name>.rodata.gPWM_MOTORClockConfig</name>
         <load_address>0x15cc</load_address>
         <readonly>true</readonly>
         <run_address>0x15cc</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-63">
         <name>.data.rxbuf</name>
         <load_address>0x202000c8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c8</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-64">
         <name>.data.cx</name>
         <load_address>0x202000c6</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.data.baseSpeed</name>
         <load_address>0x202000c4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c4</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.data.SpeedL</name>
         <load_address>0x202000c0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c0</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.data.SpeedR</name>
         <load_address>0x202000c2</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000c2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.data.Kp</name>
         <load_address>0x202000bc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.common:gPWM_MOTORBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x189</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_abbrev</name>
         <load_address>0x189</load_address>
         <run_address>0x189</run_address>
         <size>0x1e9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x372</load_address>
         <run_address>0x372</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0x3df</load_address>
         <run_address>0x3df</run_address>
         <size>0x116</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x4f5</load_address>
         <run_address>0x4f5</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_abbrev</name>
         <load_address>0x557</load_address>
         <run_address>0x557</run_address>
         <size>0x258</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0x7af</load_address>
         <run_address>0x7af</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0xa2e</load_address>
         <run_address>0xa2e</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0xadd</load_address>
         <run_address>0xadd</run_address>
         <size>0x186</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_abbrev</name>
         <load_address>0xc63</load_address>
         <run_address>0xc63</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_abbrev</name>
         <load_address>0xc9c</load_address>
         <run_address>0xc9c</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0xd5e</load_address>
         <run_address>0xd5e</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xdce</load_address>
         <run_address>0xdce</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_abbrev</name>
         <load_address>0xe5b</load_address>
         <run_address>0xe5b</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_abbrev</name>
         <load_address>0xf0e</load_address>
         <run_address>0xf0e</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_abbrev</name>
         <load_address>0xf35</load_address>
         <run_address>0xf35</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_abbrev</name>
         <load_address>0xf5c</load_address>
         <run_address>0xf5c</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_abbrev</name>
         <load_address>0xf83</load_address>
         <run_address>0xf83</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_abbrev</name>
         <load_address>0xfaa</load_address>
         <run_address>0xfaa</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_abbrev</name>
         <load_address>0xfd1</load_address>
         <run_address>0xfd1</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-98"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_abbrev</name>
         <load_address>0xff8</load_address>
         <run_address>0xff8</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-99"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_abbrev</name>
         <load_address>0x101f</load_address>
         <run_address>0x101f</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9a"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0x1046</load_address>
         <run_address>0x1046</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9b"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_abbrev</name>
         <load_address>0x106d</load_address>
         <run_address>0x106d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9c"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_abbrev</name>
         <load_address>0x1094</load_address>
         <run_address>0x1094</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9d"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_abbrev</name>
         <load_address>0x10bb</load_address>
         <run_address>0x10bb</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a1"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_abbrev</name>
         <load_address>0x10e0</load_address>
         <run_address>0x10e0</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1017</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0x1017</load_address>
         <run_address>0x1017</run_address>
         <size>0x370e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4725</load_address>
         <run_address>0x4725</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x47a5</load_address>
         <run_address>0x47a5</run_address>
         <size>0xf6e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0x5713</load_address>
         <run_address>0x5713</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_info</name>
         <load_address>0x5788</load_address>
         <run_address>0x5788</run_address>
         <size>0x2f7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_info</name>
         <load_address>0x8705</load_address>
         <run_address>0x8705</run_address>
         <size>0x1259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x995e</load_address>
         <run_address>0x995e</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_info</name>
         <load_address>0x9d81</load_address>
         <run_address>0x9d81</run_address>
         <size>0x74a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_info</name>
         <load_address>0xa4cb</load_address>
         <run_address>0xa4cb</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_info</name>
         <load_address>0xa511</load_address>
         <run_address>0xa511</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xa6a3</load_address>
         <run_address>0xa6a3</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xa769</load_address>
         <run_address>0xa769</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0xa8e9</load_address>
         <run_address>0xa8e9</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_info</name>
         <load_address>0xa9d6</load_address>
         <run_address>0xa9d6</run_address>
         <size>0x1ac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_info</name>
         <load_address>0xab82</load_address>
         <run_address>0xab82</run_address>
         <size>0x1ac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_info</name>
         <load_address>0xad2e</load_address>
         <run_address>0xad2e</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_info</name>
         <load_address>0xaec0</load_address>
         <run_address>0xaec0</run_address>
         <size>0x194</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_info</name>
         <load_address>0xb054</load_address>
         <run_address>0xb054</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_info</name>
         <load_address>0xb1e6</load_address>
         <run_address>0xb1e6</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-98"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_info</name>
         <load_address>0xb378</load_address>
         <run_address>0xb378</run_address>
         <size>0x194</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-99"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_info</name>
         <load_address>0xb50c</load_address>
         <run_address>0xb50c</run_address>
         <size>0x19a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9a"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_info</name>
         <load_address>0xb6a6</load_address>
         <run_address>0xb6a6</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9b"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_info</name>
         <load_address>0xb83e</load_address>
         <run_address>0xb83e</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9c"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0xb9d6</load_address>
         <run_address>0xb9d6</run_address>
         <size>0x19e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9d"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0xbb74</load_address>
         <run_address>0xbb74</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a1"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.debug_info</name>
         <load_address>0xbe6e</load_address>
         <run_address>0xbe6e</run_address>
         <size>0xbe</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x118</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_ranges</name>
         <load_address>0x180</load_address>
         <run_address>0x180</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_ranges</name>
         <load_address>0x1b0</load_address>
         <run_address>0x1b0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_ranges</name>
         <load_address>0x320</load_address>
         <run_address>0x320</run_address>
         <size>0x190</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x4b0</load_address>
         <run_address>0x4b0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_ranges</name>
         <load_address>0x4f8</load_address>
         <run_address>0x4f8</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x5a0</load_address>
         <run_address>0x5a0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x5b8</load_address>
         <run_address>0x5b8</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_ranges</name>
         <load_address>0x5e8</load_address>
         <run_address>0x5e8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_ranges</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a1"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb19</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_str</name>
         <load_address>0xb19</load_address>
         <run_address>0xb19</run_address>
         <size>0x26dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x31f5</load_address>
         <run_address>0x31f5</run_address>
         <size>0x175</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_str</name>
         <load_address>0x336a</load_address>
         <run_address>0x336a</run_address>
         <size>0x70c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_str</name>
         <load_address>0x3a76</load_address>
         <run_address>0x3a76</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_str</name>
         <load_address>0x3bed</load_address>
         <run_address>0x3bed</run_address>
         <size>0x1c27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x5814</load_address>
         <run_address>0x5814</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x6501</load_address>
         <run_address>0x6501</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_str</name>
         <load_address>0x6726</load_address>
         <run_address>0x6726</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_str</name>
         <load_address>0x6a55</load_address>
         <run_address>0x6a55</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_str</name>
         <load_address>0x6b4a</load_address>
         <run_address>0x6b4a</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x6ce5</load_address>
         <run_address>0x6ce5</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x6e4d</load_address>
         <run_address>0x6e4d</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_str</name>
         <load_address>0x7022</load_address>
         <run_address>0x7022</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_frame</name>
         <load_address>0xdc</load_address>
         <run_address>0xdc</run_address>
         <size>0x31c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x3f8</load_address>
         <run_address>0x3f8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x428</load_address>
         <run_address>0x428</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_frame</name>
         <load_address>0x4b4</load_address>
         <run_address>0x4b4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_frame</name>
         <load_address>0x4d4</load_address>
         <run_address>0x4d4</run_address>
         <size>0x400</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_frame</name>
         <load_address>0x8d4</load_address>
         <run_address>0x8d4</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0xa8c</load_address>
         <run_address>0xa8c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0xb1c</load_address>
         <run_address>0xb1c</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_frame</name>
         <load_address>0xc1c</load_address>
         <run_address>0xc1c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_frame</name>
         <load_address>0xc3c</load_address>
         <run_address>0xc3c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0xc74</load_address>
         <run_address>0xc74</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0xc9c</load_address>
         <run_address>0xc9c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_frame</name>
         <load_address>0xccc</load_address>
         <run_address>0xccc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0x8ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0xd5a</load_address>
         <run_address>0xd5a</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0xe19</load_address>
         <run_address>0xe19</run_address>
         <size>0x388</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_line</name>
         <load_address>0x11a1</load_address>
         <run_address>0x11a1</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_line</name>
         <load_address>0x1285</load_address>
         <run_address>0x1285</run_address>
         <size>0x15a2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_line</name>
         <load_address>0x2827</load_address>
         <run_address>0x2827</run_address>
         <size>0x989</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x31b0</load_address>
         <run_address>0x31b0</run_address>
         <size>0x1fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x33ae</load_address>
         <run_address>0x33ae</run_address>
         <size>0x4fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_line</name>
         <load_address>0x38a9</load_address>
         <run_address>0x38a9</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_line</name>
         <load_address>0x38e7</load_address>
         <run_address>0x38e7</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x39df</load_address>
         <run_address>0x39df</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x3a9e</load_address>
         <run_address>0x3a9e</run_address>
         <size>0x1c7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x3c65</load_address>
         <run_address>0x3c65</run_address>
         <size>0x6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_line</name>
         <load_address>0x3cd0</load_address>
         <run_address>0x3cd0</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_line</name>
         <load_address>0x3dd7</load_address>
         <run_address>0x3dd7</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_line</name>
         <load_address>0x3f3c</load_address>
         <run_address>0x3f3c</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_line</name>
         <load_address>0x4048</load_address>
         <run_address>0x4048</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0x4101</load_address>
         <run_address>0x4101</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0x41e1</load_address>
         <run_address>0x41e1</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-98"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_line</name>
         <load_address>0x4303</load_address>
         <run_address>0x4303</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-99"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_line</name>
         <load_address>0x43bb</load_address>
         <run_address>0x43bb</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9a"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x447b</load_address>
         <run_address>0x447b</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9b"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_line</name>
         <load_address>0x452f</load_address>
         <run_address>0x452f</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9c"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_line</name>
         <load_address>0x45eb</load_address>
         <run_address>0x45eb</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_line</name>
         <load_address>0x468f</load_address>
         <run_address>0x468f</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a1"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x18ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_loc</name>
         <load_address>0x18c0</load_address>
         <run_address>0x18c0</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x207c</load_address>
         <run_address>0x207c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_loc</name>
         <load_address>0x2154</load_address>
         <run_address>0x2154</run_address>
         <size>0x480</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_loc</name>
         <load_address>0x25d4</load_address>
         <run_address>0x25d4</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x2740</load_address>
         <run_address>0x2740</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x27af</load_address>
         <run_address>0x27af</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_loc</name>
         <load_address>0x2915</load_address>
         <run_address>0x2915</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-93"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-97"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-98"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-99"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9a"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9b"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9c"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9d"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_aranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a1"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x14f8</size>
         <contents>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-a2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x15d0</load_address>
         <run_address>0x15d0</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-188"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x15b8</load_address>
         <run_address>0x15b8</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-123"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-151"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000bc</run_address>
         <size>0xe</size>
         <contents>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-fa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <contents>
            <object_component_ref idref="oc-c7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-18c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-148" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-149" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14a" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14b" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14c" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14d" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14f" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-16b" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10ef</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-18e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16d" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbf2c</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-18d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16f" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x628</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-83"/>
         </contents>
      </logical_group>
      <logical_group id="lg-171" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7161</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-fe"/>
         </contents>
      </logical_group>
      <logical_group id="lg-173" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcfc</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-175" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x472f</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-84"/>
         </contents>
      </logical_group>
      <logical_group id="lg-177" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x293b</size>
         <contents>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-a8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-181" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x188</size>
         <contents>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-86"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18b" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-198" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1608</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-199" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0xca</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-19a" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1608</used_space>
         <unused_space>0x1e9f8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x14f8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x15b8</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x15d0</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1608</start_address>
               <size>0x1e9f8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x2ca</used_space>
         <unused_space>0x7d36</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-14d"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-14f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xbc</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202000bc</start_address>
               <size>0xe</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202000ca</start_address>
               <size>0x7d36</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x15d0</load_address>
            <load_size>0x11</load_size>
            <run_address>0x202000bc</run_address>
            <run_size>0xe</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x15f0</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xbc</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x15f8</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1608</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1608</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x15e4</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x15f0</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-57">
         <name>main</name>
         <value>0xdad</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-58">
         <name>Motor_Ctrl</name>
         <value>0x5e9</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-59">
         <name>baseSpeed</name>
         <value>0x202000c4</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-5a">
         <name>cx</name>
         <value>0x202000c6</value>
         <object_component_ref idref="oc-64"/>
      </symbol>
      <symbol id="sm-5b">
         <name>Kp</name>
         <value>0x202000bc</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-5c">
         <name>SpeedL</name>
         <value>0x202000c0</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-5d">
         <name>SpeedR</name>
         <value>0x202000c2</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-5e">
         <name>UART1_IRQHandler</name>
         <value>0xbb5</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-5f">
         <name>rxbuf</name>
         <value>0x202000c8</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-e2">
         <name>SYSCFG_DL_init</name>
         <value>0x11dd</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-e3">
         <name>SYSCFG_DL_initPower</name>
         <value>0xe21</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-e4">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x6e1</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-e5">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x113d</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-e6">
         <name>SYSCFG_DL_PWM_MOTOR_init</name>
         <value>0x995</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-e7">
         <name>SYSCFG_DL_UART_OPENMV_init</name>
         <value>0xf0d</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-e8">
         <name>gPWM_MOTORBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-f5">
         <name>Default_Handler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f6">
         <name>Reset_Handler</name>
         <value>0x15ad</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-f7">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-f8">
         <name>NMI_Handler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-f9">
         <name>HardFault_Handler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fa">
         <name>SVC_Handler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fb">
         <name>PendSV_Handler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fc">
         <name>SysTick_Handler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fd">
         <name>GROUP0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-fe">
         <name>GROUP1_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ff">
         <name>TIMG8_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-100">
         <name>UART3_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-101">
         <name>ADC0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-102">
         <name>ADC1_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-103">
         <name>CANFD0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-104">
         <name>DAC0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-105">
         <name>SPI0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-106">
         <name>SPI1_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-107">
         <name>UART2_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-108">
         <name>UART0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-109">
         <name>TIMG0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10a">
         <name>TIMG6_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10b">
         <name>TIMA0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10c">
         <name>TIMA1_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10d">
         <name>TIMG7_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10e">
         <name>TIMG12_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10f">
         <name>I2C0_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-110">
         <name>I2C1_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-111">
         <name>AES_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-112">
         <name>RTC_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-113">
         <name>DMA_IRQHandler</name>
         <value>0x1325</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12a">
         <name>Motor_On</name>
         <value>0x11b9</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-12b">
         <name>Motor_Off</name>
         <value>0xcbd</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-12c">
         <name>Set_Speed</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-12d">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12e">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12f">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-130">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-131">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-132">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-133">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-134">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-135">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-13e">
         <name>DL_Common_delayCycles</name>
         <value>0x1599</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-158">
         <name>DL_Timer_setClockConfig</name>
         <value>0x12ed</value>
         <object_component_ref idref="oc-11d"/>
      </symbol>
      <symbol id="sm-159">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x1589</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-15a">
         <name>DL_Timer_initPWMMode</name>
         <value>0xa65</value>
         <object_component_ref idref="oc-146"/>
      </symbol>
      <symbol id="sm-15b">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1419</value>
         <object_component_ref idref="oc-11f"/>
      </symbol>
      <symbol id="sm-15c">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x12d1</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-15d">
         <name>DL_TimerA_initPWMMode</name>
         <value>0xc3d</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-16d">
         <name>DL_UART_init</name>
         <value>0xec5</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-16e">
         <name>DL_UART_setClockConfig</name>
         <value>0x1543</value>
         <object_component_ref idref="oc-125"/>
      </symbol>
      <symbol id="sm-16f">
         <name>DL_UART_transmitDataBlocking</name>
         <value>0x1221</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-17a">
         <name>_c_int00_noargs</name>
         <value>0x1191</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-17b">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-187">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xfd5</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-18f">
         <name>_system_pre_init</name>
         <value>0x15b1</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-19a">
         <name>__TI_zero_init_nomemset</name>
         <value>0x148d</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-1a3">
         <name>__TI_decompress_none</name>
         <value>0x1567</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-1ae">
         <name>__TI_decompress_lzss</name>
         <value>0xd35</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1b8">
         <name>abort</name>
         <value>0x15b5</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-1b9">
         <name>C$$EXIT</name>
         <value>0x15b4</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-1c1">
         <name>__aeabi_fadd</name>
         <value>0x8c7</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-1c2">
         <name>__addsf3</name>
         <value>0x8c7</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>__aeabi_fsub</name>
         <value>0x8bd</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>__subsf3</name>
         <value>0x8bd</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-1ca">
         <name>__aeabi_dadd</name>
         <value>0x353</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-1cb">
         <name>__adddf3</name>
         <value>0x353</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-1cc">
         <name>__aeabi_dsub</name>
         <value>0x349</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>__subdf3</name>
         <value>0x349</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-1d3">
         <name>__aeabi_dmul</name>
         <value>0x7d9</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-1d4">
         <name>__muldf3</name>
         <value>0x7d9</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-1da">
         <name>__muldsi3</name>
         <value>0x1011</value>
         <object_component_ref idref="oc-12f"/>
      </symbol>
      <symbol id="sm-1e0">
         <name>__aeabi_fmul</name>
         <value>0xb29</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-1e1">
         <name>__mulsf3</name>
         <value>0xb29</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-1e7">
         <name>__aeabi_ddiv</name>
         <value>0x4dd</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-1e8">
         <name>__divdf3</name>
         <value>0x4dd</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-1ee">
         <name>__aeabi_f2iz</name>
         <value>0x104d</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-1ef">
         <name>__fixsfsi</name>
         <value>0x104d</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>__aeabi_d2uiz</name>
         <value>0xf55</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-1f6">
         <name>__fixunsdfsi</name>
         <value>0xf55</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>__aeabi_i2d</name>
         <value>0x1111</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>__floatsidf</name>
         <value>0x1111</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-203">
         <name>__aeabi_i2f</name>
         <value>0xf99</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-204">
         <name>__floatsisf</name>
         <value>0xf99</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-20a">
         <name>__aeabi_memcpy</name>
         <value>0x15a5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-20b">
         <name>__aeabi_memcpy4</name>
         <value>0x15a5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-20c">
         <name>__aeabi_memcpy8</name>
         <value>0x15a5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-216">
         <name>TI_memcpy_small</name>
         <value>0x1555</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-217">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-21a">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-21b">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
